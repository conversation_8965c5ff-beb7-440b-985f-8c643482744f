const fs = require('fs');
const path = require('path');

// Function to fix imports in a file with correct relative paths
function fixImportsInFile(filePath, correctDepth) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Replace all @/ imports and incorrect relative imports with correct ones
    let fixedContent = content
      // Fix @/ imports
      .replace(/@\/components\/ui\//g, `${correctDepth}components/ui/`)
      .replace(/@\/components\//g, `${correctDepth}components/`)
      .replace(/@\/lib\//g, `${correctDepth}lib/`)
      .replace(/@\/hooks\//g, `${correctDepth}hooks/`)
      .replace(/@\/contexts\//g, `${correctDepth}contexts/`)
      // Fix incorrect relative imports (3 levels when should be 4)
      .replace(/\.\.\/\.\.\/\.\.\/components\/ui\//g, `${correctDepth}components/ui/`)
      .replace(/\.\.\/\.\.\/\.\.\/components\//g, `${correctDepth}components/`)
      .replace(/\.\.\/\.\.\/\.\.\/lib\//g, `${correctDepth}lib/`)
      .replace(/\.\.\/\.\.\/\.\.\/hooks\//g, `${correctDepth}hooks/`)
      .replace(/\.\.\/\.\.\/\.\.\/contexts\//g, `${correctDepth}contexts/`);
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed imports in: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Files and their correct relative path depths
const filesToFix = [
  // Main dashboard pages (4 levels up)
  { file: 'app/(dashboard)/dashboard/cabinets/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/courses/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/students/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/teachers/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/users/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/leads/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/groups/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/communication/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/unauthorized/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/enrollments/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/settings/page.tsx', depth: '../../../../' },
  
  // Root dashboard page (3 levels up)
  { file: 'app/(dashboard)/dashboard/page.tsx', depth: '../../../' },
  
  // Nested pages (5 levels up)
  { file: 'app/(dashboard)/dashboard/students/[id]/page.tsx', depth: '../../../../../' },
  { file: 'app/(dashboard)/dashboard/groups/[id]/page.tsx', depth: '../../../../../' },
  { file: 'app/(dashboard)/dashboard/teachers/[id]/page.tsx', depth: '../../../../../' },
  { file: 'app/(dashboard)/dashboard/cabinets/[id]/page.tsx', depth: '../../../../../' },
  
  // Component files (2 levels up)
  { file: 'components/dashboard/sidebar.tsx', depth: '../../' },
  { file: 'components/dashboard/header.tsx', depth: '../../' },
  { file: 'components/dashboard/activity-feed.tsx', depth: '../../' }
];

console.log('🔧 Fixing all UI component imports...\n');

let fixedCount = 0;
filesToFix.forEach(({ file, depth }) => {
  if (fs.existsSync(file)) {
    if (fixImportsInFile(file, depth)) {
      fixedCount++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

console.log(`\n🎯 Summary: Fixed imports in ${fixedCount} files`);
