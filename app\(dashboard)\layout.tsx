import { Sidebar } from '../../components/dashboard/sidebar'
import { Head<PERSON> } from '../../components/dashboard/header'
import { Toaster } from '../../components/ui/toaster'
import { BranchProvider } from '../../contexts/branch-context'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <BranchProvider>
      <div className="flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header />
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-transparent p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
              <div className="fade-in">
                {children}
              </div>
            </div>
          </main>
        </div>
        <Toaster />
      </div>
    </BranchProvider>
  )
}
