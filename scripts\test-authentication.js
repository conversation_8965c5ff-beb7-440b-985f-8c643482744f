const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testAuthentication() {
  console.log('🔍 Testing Authentication System...\n')

  try {
    // Test database connection
    console.log('1. Testing database connection...')
    await prisma.$connect()
    console.log('✅ Database connection successful\n')

    // Check if users exist
    console.log('2. Checking existing users...')
    const users = await prisma.user.findMany({
      select: {
        id: true,
        phone: true,
        name: true,
        role: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'asc' },
      take: 10,
    })

    if (users.length === 0) {
      console.log('❌ No users found in database')
      console.log('💡 Run: npm run db:seed to create test users\n')
      return
    }

    console.log(`✅ Found ${users.length} users:`)
    users.forEach(user => {
      console.log(`   - ${user.name} (${user.phone}) - ${user.role}`)
    })
    console.log('')

    // Test password verification for default users
    console.log('3. Testing default user credentials...')
    const testCredentials = [
      { phone: '+998901234567', password: 'admin123', expectedRole: 'ADMIN' },
      { phone: '+998901234568', password: 'manager123', expectedRole: 'MANAGER' },
      { phone: '+998901234569', password: 'reception123', expectedRole: 'RECEPTION' },
    ]

    for (const cred of testCredentials) {
      try {
        const user = await prisma.user.findUnique({
          where: { phone: cred.phone },
          select: {
            id: true,
            phone: true,
            name: true,
            role: true,
            password: true,
          },
        })

        if (user) {
          const isPasswordValid = await bcrypt.compare(cred.password, user.password)
          if (isPasswordValid) {
            console.log(`✅ ${cred.phone} (${user.role}) - Password valid`)
          } else {
            console.log(`❌ ${cred.phone} (${user.role}) - Password invalid`)
          }
        } else {
          console.log(`❌ ${cred.phone} - User not found`)
        }
      } catch (error) {
        console.log(`❌ ${cred.phone} - Error: ${error.message}`)
      }
    }
    console.log('')

    // Check environment variables
    console.log('4. Checking environment variables...')
    const requiredEnvVars = [
      'DATABASE_URL',
      'NEXTAUTH_SECRET',
      'NEXTAUTH_URL',
    ]

    const optionalEnvVars = [
      'SERVER_TYPE',
      'ADMIN_SERVER_URL',
      'STAFF_SERVER_URL',
      'INTER_SERVER_SECRET',
    ]

    console.log('Required variables:')
    requiredEnvVars.forEach(varName => {
      const value = process.env[varName]
      if (value) {
        console.log(`✅ ${varName}: ${varName === 'DATABASE_URL' ? '[HIDDEN]' : value}`)
      } else {
        console.log(`❌ ${varName}: Not set`)
      }
    })

    console.log('\nOptional variables (for inter-server communication):')
    optionalEnvVars.forEach(varName => {
      const value = process.env[varName]
      if (value) {
        console.log(`✅ ${varName}: ${varName.includes('SECRET') ? '[HIDDEN]' : value}`)
      } else {
        console.log(`⚠️  ${varName}: Not set`)
      }
    })
    console.log('')

    // Test authentication endpoint
    console.log('5. Testing authentication endpoint...')
    try {
      const response = await fetch('http://localhost:3000/api/auth/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone: '+998901234567',
          password: 'admin123',
          serverKey: process.env.INTER_SERVER_SECRET || 'test-key',
        }),
      })

      if (response.ok) {
        const data = await response.json()
        console.log('✅ Auth endpoint working:', data.success ? 'Success' : 'Failed')
      } else {
        console.log(`❌ Auth endpoint failed: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.log(`⚠️  Auth endpoint test skipped: ${error.message}`)
    }

    console.log('\n🎯 Authentication Test Summary:')
    console.log('1. Make sure all required environment variables are set')
    console.log('2. Ensure users exist in database (run npm run db:seed if needed)')
    console.log('3. Verify passwords match the default credentials')
    console.log('4. Check that NEXTAUTH_SECRET is set and secure')
    console.log('5. For inter-server communication, set INTER_SERVER_SECRET')

  } catch (error) {
    console.error('❌ Authentication test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test
testAuthentication().catch(console.error)
