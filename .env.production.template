# PRODUCTION ENVIRONMENT VARIABLES
# Copy these to Vercel Dashboard → Settings → Environment Variables

# Database Configuration
DATABASE_URL="your-production-database-url"
PRISMA_GENERATE_DATAPROXY="true"

# Authentication
NEXTAUTH_SECRET="your-32-character-secret-key-here"
NEXTAUTH_URL="https://your-app.vercel.app"

# Application Configuration
APP_NAME="Innovative Centre - Admin Portal"
APP_URL="https://your-app.vercel.app"
APP_ENV="production"
SERVER_TYPE="admin"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="your-inter-server-secret-key"

# Optional: SMS/Email Configuration
SMS_PROVIDER="eskiz"
SMS_API_KEY="your-sms-api-key"
EMAIL_PROVIDER="gmail"
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-app-password"
