import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '../../../../lib/auth'
import { prisma } from '../../../../lib/prisma'
import { ActivityLogger } from '../../../../lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const updateAttendanceSchema = z.object({
  status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']).optional(),
  notes: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const attendance = await prisma.attendance.findUnique({
      where: { id },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        class: {
          include: {
            group: {
              include: {
                course: {
                  select: {
                    name: true,
                    level: true,
                  },
                },
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!attendance) {
      return NextResponse.json({ error: 'Attendance record not found' }, { status: 404 })
    }

    return NextResponse.json(attendance)
  } catch (error) {
    console.error('Error fetching attendance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers, admins, and managers can update attendance
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateAttendanceSchema.parse(body)

    // Check if attendance exists
    const existingAttendance = await prisma.attendance.findUnique({
      where: { id },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        class: {
          include: {
            group: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    })

    if (!existingAttendance) {
      return NextResponse.json({ error: 'Attendance record not found' }, { status: 404 })
    }

    const attendance = await prisma.attendance.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        class: {
          include: {
            group: {
              include: {
                course: {
                  select: {
                    name: true,
                    level: true,
                  },
                },
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'UPDATE',
      resource: 'attendance',
      resourceId: attendance.id,
      details: {
        changes: validatedData,
        studentName: attendance.student?.user.name || 'Unknown',
        className: attendance.class?.group?.name || 'Unknown',
        previousStatus: existingAttendance.status,
        newStatus: attendance.status,
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json(attendance)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating attendance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admins and managers can delete attendance records
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params

    // Check if attendance exists
    const existingAttendance = await prisma.attendance.findUnique({
      where: { id },
      include: {
        student: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        class: {
          include: {
            group: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    })

    if (!existingAttendance) {
      return NextResponse.json({ error: 'Attendance record not found' }, { status: 404 })
    }

    await prisma.attendance.delete({
      where: { id },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'DELETE',
      resource: 'attendance',
      resourceId: id,
      details: {
        studentName: existingAttendance.student?.user.name || 'Unknown',
        className: existingAttendance.class?.group?.name || 'Unknown',
        status: existingAttendance.status,
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json({ message: 'Attendance record deleted successfully' })
  } catch (error) {
    console.error('Error deleting attendance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
