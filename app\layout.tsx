import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { AuthProvider } from '../components/providers/auth-provider'
import { QueryProvider } from '../components/providers/query-provider'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Innovative Centre CRM',
  description: 'Customer Relationship Management System for Innovative Centre',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <QueryProvider>
          <AuthProvider>{children}</AuthProvider>
        </QueryProvider>
      </body>
    </html>
  )
}
