const fs = require('fs');
const path = require('path');

// Function to fix all malformed imports in a file
function fixAllMalformedImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Fix all variations of malformed paths
    let fixedContent = content
      // Fix patterns like '../../../@/' or '../../@/' etc.
      .replace(/\.\.\/+@\//g, '@/')
      // Fix patterns like '../../../../@/' etc.
      .replace(/(\.\.\/)+(.*?)@\//g, '@/')
      // Fix any remaining malformed patterns
      .replace(/[\.\/]+@\//g, '@/');
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed all malformed imports in: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No malformed imports found: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to recursively find all .tsx and .ts files
function findAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      findAllFiles(filePath, fileList);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

console.log('🔧 Finding and fixing all malformed imports...\n');

// Find all TypeScript/React files
const allFiles = findAllFiles(process.cwd());
const relevantFiles = allFiles.filter(file =>
  file.includes('app') || file.includes('components') || file.includes('lib')
);

let fixedCount = 0;
relevantFiles.forEach(file => {
  if (fixAllMalformedImports(file)) {
    fixedCount++;
  }
});

console.log(`\n🎯 Summary: Fixed malformed imports in ${fixedCount} files out of ${relevantFiles.length} checked`);
