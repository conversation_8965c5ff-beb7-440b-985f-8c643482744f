import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../../lib/prisma'
import * as z from 'zod'

const updateEnrollmentSchema = z.object({
  status: z.enum(['ACTIVE', 'COMPLETED', 'DROPPED', 'SUSPENDED']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const enrollment = await prisma.enrollment.findUnique({
      where: { id },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
            payments: {
              where: {
                // Get payments related to this enrollment period
                createdAt: {
                  gte: new Date(new Date().setMonth(new Date().getMonth() - 3)), // Last 3 months
                },
              },
              orderBy: { createdAt: 'desc' },
            },
          },
        },
        group: {
          include: {
            course: {
              select: {
                id: true,
                name: true,
                level: true,
                description: true,
                duration: true,
                price: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    phone: true,
                    email: true,
                  },
                },
              },
            },
            classes: {
              include: {
                attendances: {
                  where: {
                    studentId: id, // Get attendance for this specific student
                  },
                },
              },
              orderBy: { date: 'desc' },
              take: 10,
            },
          },
        },
      },
    })

    if (!enrollment) {
      return NextResponse.json(
        { error: 'Enrollment not found' },
        { status: 404 }
      )
    }

    // Calculate attendance statistics
    const totalClasses = enrollment.group.classes.length
    const attendedClasses = enrollment.group.classes.filter(
      cls => cls.attendances.some(att => att.status === 'PRESENT')
    ).length
    const attendanceRate = totalClasses > 0 ? (attendedClasses / totalClasses) * 100 : 0

    return NextResponse.json({
      ...enrollment,
      attendanceStats: {
        totalClasses,
        attendedClasses,
        attendanceRate: Math.round(attendanceRate * 100) / 100,
      },
    })
  } catch (error) {
    console.error('Error fetching enrollment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = updateEnrollmentSchema.parse(body)

    // Check if enrollment exists
    const existingEnrollment = await prisma.enrollment.findUnique({
      where: { id },
    })

    if (!existingEnrollment) {
      return NextResponse.json(
        { error: 'Enrollment not found' },
        { status: 404 }
      )
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      updatedAt: new Date(),
    }

    if (validatedData.startDate) {
      updateData.startDate = new Date(validatedData.startDate)
    }

    if (validatedData.endDate) {
      updateData.endDate = new Date(validatedData.endDate)
    }

    // Auto-set endDate when status changes to COMPLETED or DROPPED
    if (validatedData.status && ['COMPLETED', 'DROPPED'].includes(validatedData.status)) {
      if (!validatedData.endDate) {
        updateData.endDate = new Date()
      }
    }

    const enrollment = await prisma.enrollment.update({
      where: { id },
      data: updateData,
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    return NextResponse.json(enrollment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating enrollment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if enrollment exists
    const existingEnrollment = await prisma.enrollment.findUnique({
      where: { id },
    })

    if (!existingEnrollment) {
      return NextResponse.json(
        { error: 'Enrollment not found' },
        { status: 404 }
      )
    }

    // Check if enrollment is active - might want to prevent deletion
    if (existingEnrollment.status === 'ACTIVE') {
      return NextResponse.json(
        { 
          error: 'Cannot delete active enrollment',
          details: 'Please change status to DROPPED or COMPLETED before deletion'
        },
        { status: 400 }
      )
    }

    // Delete enrollment
    await prisma.enrollment.delete({
      where: { id },
    })

    return NextResponse.json({
      message: 'Enrollment deleted successfully',
      deletedId: id
    })
  } catch (error) {
    console.error('Error deleting enrollment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
