const fs = require('fs');
const path = require('path');

// Function to calculate relative path depth
function getRelativePath(filePath) {
  const depth = filePath.split('/').length - 2; // -2 for app and filename
  return '../'.repeat(depth);
}

// Function to fix imports in a file
function fixImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = getRelativePath(filePath);
    
    // Replace @/components/ui/ imports with relative paths
    const fixedContent = content.replace(
      /@\/components\/ui\//g,
      `${relativePath}components/ui/`
    ).replace(
      /@\/components\//g,
      `${relativePath}components/`
    ).replace(
      /@\/lib\//g,
      `${relativePath}lib/`
    ).replace(
      /@\/hooks\//g,
      `${relativePath}hooks/`
    ).replace(
      /@\/contexts\//g,
      `${relativePath}contexts/`
    );
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed imports in: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// List of critical dashboard pages to fix
const criticalPages = [
  'app/(dashboard)/dashboard/page.tsx',
  'app/(dashboard)/dashboard/cabinets/page.tsx',
  'app/(dashboard)/dashboard/courses/page.tsx',
  'app/(dashboard)/dashboard/students/page.tsx',
  'app/(dashboard)/dashboard/teachers/page.tsx',
  'app/(dashboard)/dashboard/users/page.tsx',
  'app/(dashboard)/dashboard/leads/page.tsx',
  'app/(dashboard)/dashboard/groups/page.tsx',
  'app/(dashboard)/dashboard/communication/page.tsx',
  'app/(dashboard)/dashboard/unauthorized/page.tsx',
  'app/(dashboard)/dashboard/students/[id]/page.tsx',
  'app/(dashboard)/dashboard/groups/[id]/page.tsx',
  'app/(dashboard)/dashboard/teachers/[id]/page.tsx',
  'app/(dashboard)/dashboard/enrollments/page.tsx',
  'app/(dashboard)/dashboard/settings/page.tsx',
  'components/dashboard/sidebar.tsx',
  'components/dashboard/header.tsx',
  'components/dashboard/activity-feed.tsx'
];

console.log('🔧 Fixing UI component imports...\n');

let fixedCount = 0;
criticalPages.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    if (fixImportsInFile(filePath)) {
      fixedCount++;
    }
  } else {
    console.log(`⚠️  File not found: ${filePath}`);
  }
});

console.log(`\n🎯 Summary: Fixed imports in ${fixedCount} files`);
