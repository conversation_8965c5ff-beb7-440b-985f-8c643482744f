# 🚀 Automated Deployment Guide for Inno-CRM

This guide provides comprehensive instructions for deploying your CRM system to Vercel with full automation.

## 📋 Prerequisites

- Node.js 18+ installed
- Git repository initialized
- Vercel account created
- Production database (Neon PostgreSQL recommended)

## 🎯 Quick Start (Fully Automated)

### Option 1: Complete Automation
```bash
# 1. Clean any previous deployment attempts
npm run deploy:clean

# 2. Set up environment configuration (interactive)
npm run deploy:setup

# 3. Validate everything is ready
npm run deploy:validate

# 4. Deploy automatically
npm run deploy:auto

# 5. Verify deployment
npm run verify:deployment
```

### Option 2: Manual Step-by-Step

#### Step 1: Clean Previous Deployments
```bash
npm run deploy:clean
```

#### Step 2: Install Dependencies
```bash
npm install
```

#### Step 3: Configure Environment
```bash
npm run deploy:setup
```
This interactive script will:
- Detect server type (admin/staff)
- Configure database connection
- Generate secure secrets
- Set up inter-server communication
- Create environment files

#### Step 4: Validate Configuration
```bash
npm run deploy:validate
```

#### Step 5: Deploy to Vercel
```bash
# Option A: Use automation script
npm run deploy:auto

# Option B: Manual deployment
chmod +x scripts/set-vercel-env.sh
./scripts/set-vercel-env.sh
vercel --prod
```

#### Step 6: Verify Deployment
```bash
npm run verify:deployment
```

## 🔧 Environment Variables

### Required Variables
- `DATABASE_URL` - Production PostgreSQL connection string
- `NEXTAUTH_SECRET` - 32+ character secret for NextAuth
- `NEXTAUTH_URL` - Your deployment URL
- `PRISMA_GENERATE_DATAPROXY` - Must be "true" for Vercel
- `SERVER_TYPE` - "admin" or "staff"

### Inter-Server Communication
- `ADMIN_SERVER_URL` - URL of admin server
- `STAFF_SERVER_URL` - URL of staff server  
- `INTER_SERVER_SECRET` - Shared secret for server communication

### Optional Variables
- `SMS_PROVIDER` - SMS service provider
- `SMS_API_KEY` - SMS API key
- `EMAIL_PROVIDER` - Email service provider
- `EMAIL_USER` - Email username
- `EMAIL_PASSWORD` - Email password

## 🗄️ Database Setup

### Neon PostgreSQL (Recommended)
1. Create account at [neon.tech](https://neon.tech)
2. Create new project
3. Copy connection string
4. Ensure it includes `?sslmode=require`

Example format:
```
postgresql://username:<EMAIL>/neondb?sslmode=require
```

### Database Migration
The deployment scripts automatically handle:
- Prisma client generation
- Schema validation
- Connection testing

## 🔐 Security Configuration

### Authentication Secrets
- `NEXTAUTH_SECRET`: Generated automatically (32+ characters)
- `INTER_SERVER_SECRET`: Generated automatically for server communication

### CORS Configuration
Automatically configured for:
- Admin server ↔ Staff server communication
- Secure headers
- Production security policies

## 🌐 Dual-Server Deployment

### Admin Server
```bash
# Set server type
export SERVER_TYPE=admin

# Deploy admin server
npm run deploy:auto
```

### Staff Server
```bash
# Set server type  
export SERVER_TYPE=staff

# Deploy staff server
npm run deploy:auto
```

### Inter-Server Communication
Both servers must share:
- Same `INTER_SERVER_SECRET`
- Correct server URLs
- Same database

## 🔍 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check build locally
npm run build

# Common fixes
npm run deploy:clean
npm install
npm run deploy:validate
```

#### Database Connection Issues
- Verify DATABASE_URL format
- Check database accessibility
- Ensure SSL mode is enabled
- Test connection locally

#### Environment Variable Issues
```bash
# Validate all variables
npm run deploy:validate

# Reset and reconfigure
npm run deploy:clean
npm run deploy:setup
```

#### Prisma Issues
- Ensure `PRISMA_GENERATE_DATAPROXY="true"`
- Check schema syntax
- Verify client generation

### Debug Commands
```bash
# Check deployment logs
vercel logs

# Test health endpoint
curl https://your-app.vercel.app/api/health

# Validate environment
npm run deploy:validate
```

## 📊 Verification Checklist

After deployment, verify:

- [ ] Health endpoint responds: `/api/health`
- [ ] Login page loads: `/auth/signin`
- [ ] Database connection works
- [ ] Authentication functions
- [ ] Inter-server communication (if staff server)
- [ ] All environment variables set
- [ ] No console errors
- [ ] SSL certificate valid

## 🔄 Updating Deployment

### Code Updates
```bash
git add .
git commit -m "Update deployment"
vercel --prod
```

### Environment Updates
```bash
# Update specific variable
vercel env add VARIABLE_NAME "new-value" production

# Or use setup script
npm run deploy:setup
```

### Full Redeployment
```bash
npm run deploy:clean
npm run deploy:auto
```

## 📞 Support

### Automated Scripts
- `deploy:clean` - Clean previous deployments
- `deploy:setup` - Interactive environment setup
- `deploy:validate` - Validate configuration
- `deploy:auto` - Fully automated deployment
- `verify:deployment` - Post-deployment verification

### Manual Commands
- `vercel --prod` - Deploy to production
- `vercel logs` - View deployment logs
- `vercel env ls` - List environment variables
- `vercel domains` - Manage custom domains

## 🎉 Success!

Once deployed successfully:
1. Your CRM will be available at your Vercel URL
2. Health check: `https://your-app.vercel.app/api/health`
3. Login: `https://your-app.vercel.app/auth/signin`
4. Admin panel: Available based on user roles

Remember to:
- Keep environment variables secure
- Monitor deployment logs
- Test all functionality
- Set up monitoring and alerts
