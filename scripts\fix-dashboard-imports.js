const fs = require('fs');

// Function to fix imports in a file with correct depth
function fixImportsInFile(filePath, correctDepth) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Replace all incorrect relative imports with correct ones
    let fixedContent = content
      // Fix any number of ../ back to correct depth
      .replace(/\.\.\/+components\/ui\//g, `${correctDepth}components/ui/`)
      .replace(/\.\.\/+components\//g, `${correctDepth}components/`)
      .replace(/\.\.\/+lib\//g, `${correctDepth}lib/`)
      .replace(/\.\.\/+hooks\//g, `${correctDepth}hooks/`)
      .replace(/\.\.\/+contexts\//g, `${correctDepth}contexts/`);
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed imports in: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Files and their correct relative path depths
const filesToFix = [
  // Main dashboard pages (4 levels up)
  { file: 'app/(dashboard)/dashboard/courses/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/students/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/teachers/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/users/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/leads/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/groups/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/unauthorized/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/enrollments/page.tsx', depth: '../../../../' },
  { file: 'app/(dashboard)/dashboard/settings/page.tsx', depth: '../../../../' },
];

console.log('🔧 Fixing dashboard page imports...\n');

let fixedCount = 0;
filesToFix.forEach(({ file, depth }) => {
  if (fs.existsSync(file)) {
    if (fixImportsInFile(file, depth)) {
      fixedCount++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

console.log(`\n🎯 Summary: Fixed imports in ${fixedCount} files`);
