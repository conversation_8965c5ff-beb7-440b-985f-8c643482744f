'use client'

import { use<PERSON>tate, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card'
import { Button } from '../../../../components/ui/button'
import { Badge } from '../../../../components/ui/badge'
import { Input } from '../../../../components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../../components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../../components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../../../../components/ui/dialog'
import { Alert, AlertDescription } from '../../../../components/ui/alert'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../../../components/ui/tabs'
import { formatDate } from '../../../../lib/utils'
import {
  Search, Plus, User, Phone, Mail, Calendar, Edit, Trash2, Loader2,
  Users, UserCheck, UserX, UserMinus, CreditCard, TrendingUp,
  AlertCircle, CheckCircle, Clock, Pause
} from 'lucide-react'
import StudentForm from '../../../../components/forms/student-form'
import Link from 'next/link'
import { useBranch } from '../../../../contexts/branch-context'

interface Student {
  id: string
  user: {
    id: string
    name: string
    phone: string
    email?: string
    createdAt: string
  }
  level: string
  branch: string
  status: 'ACTIVE' | 'DROPPED' | 'PAUSED' | 'COMPLETED'
  emergencyContact?: string
  dateOfBirth?: string
  address?: string
  droppedAt?: string
  pausedAt?: string
  resumedAt?: string
  reEnrollmentNotes?: string
  lastContactedAt?: string
  createdAt: string

  enrollments: {
    id: string
    status: string
    group: {
      name: string
      course: {
        name: string
        level: string
      }
    }
  }[]
  payments: {
    status: string
    amount: number
    dueDate?: string
  }[]
  paymentStatus: 'PAID' | 'UNPAID'
  unpaidAmount: number
}

interface StatusCounts {
  ACTIVE: number
  DROPPED: number
  PAUSED: number
  COMPLETED: number
}

export default function StudentsPage() {
  const { currentBranch } = useBranch()
  const [students, setStudents] = useState<Student[]>([])
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({
    ACTIVE: 0,
    DROPPED: 0,
    PAUSED: 0,
    COMPLETED: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [paymentFilter, setPaymentFilter] = useState<string>('ALL')
  const [showDroppedStudents, setShowDroppedStudents] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingStudent, setEditingStudent] = useState<Student | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (currentBranch?.id) {
      fetchStudents()
    }
  }, [statusFilter, paymentFilter, showDroppedStudents, currentBranch?.id]) // eslint-disable-line react-hooks/exhaustive-deps

  const fetchStudents = async () => {
    if (!currentBranch?.id) return

    try {
      setLoading(true)
      const params = new URLSearchParams({
        branch: currentBranch.id
      })

      if (statusFilter !== 'ALL') {
        params.append('status', statusFilter)
      }

      if (paymentFilter !== 'ALL') {
        params.append('paymentStatus', paymentFilter)
      }

      if (showDroppedStudents) {
        params.append('includeDropped', 'true')
      }

      const response = await fetch(`/api/students?${params.toString()}`)
      const data = await response.json()
      setStudents(data.students || [])
      setStatusCounts(data.statusCounts || {})
      setError(null)
    } catch (error) {
      console.error('Error fetching students:', error)
      setError('Failed to fetch students')
    } finally {
      setLoading(false)
    }
  }

  const filteredStudents = students.filter(student =>
    student.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.user.phone.includes(searchTerm) ||
    student.user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Calculate totals for KPI cards
  const totalStudents = Object.values(statusCounts).reduce((sum, count) => sum + count, 0)
  const activeStudents = statusCounts.ACTIVE || 0
  const droppedStudents = statusCounts.DROPPED || 0
  const pausedStudents = statusCounts.PAUSED || 0
  const unpaidStudents = students.filter(s => s.paymentStatus === 'UNPAID').length

  const getLevelColor = (level: string) => {
    const colors: { [key: string]: string } = {
      'A1': 'bg-red-100 text-red-800',
      'A2': 'bg-orange-100 text-orange-800',
      'B1': 'bg-yellow-100 text-yellow-800',
      'B2': 'bg-green-100 text-green-800',
      'IELTS': 'bg-indigo-100 text-indigo-800',
      'SAT': 'bg-cyan-100 text-cyan-800',
      'MATH': 'bg-emerald-100 text-emerald-800',
      'KIDS': 'bg-pink-100 text-pink-800',
    }
    return colors[level] || 'bg-gray-100 text-gray-800'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800'
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800'
      case 'DROPPED':
        return 'bg-red-100 text-red-800'
      case 'PAUSED':
        return 'bg-yellow-100 text-yellow-800'
      case 'SUSPENDED':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'DROPPED':
        return <UserX className="h-4 w-4 text-red-600" />
      case 'PAUSED':
        return <Pause className="h-4 w-4 text-yellow-600" />
      case 'COMPLETED':
        return <UserCheck className="h-4 w-4 text-blue-600" />
      default:
        return <User className="h-4 w-4 text-gray-600" />
    }
  }

  const getPaymentStatusColor = (status: string) => {
    return status === 'UNPAID' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
  }

  // Handle student creation
  const handleCreateStudent = async (data: any) => {
    setIsSubmitting(true)
    setError(null)

    try {
      // First create the user
      const userResponse = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: data.name,
          phone: data.phone,
          email: data.email || null,
          role: 'STUDENT',
          password: 'defaultPassword123' // You might want to generate this or ask for it
        })
      })

      if (!userResponse.ok) {
        const errorData = await userResponse.json()
        throw new Error(errorData.error || 'Failed to create user')
      }

      const userData = await userResponse.json()

      // Then create the student profile
      const studentResponse = await fetch('/api/students', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userData.id,
          level: data.level,
          branch: data.branch, // Use branch from form data
          emergencyContact: data.emergencyContact,
          dateOfBirth: data.dateOfBirth,
          address: data.address
        })
      })

      if (!studentResponse.ok) {
        const errorData = await studentResponse.json()
        throw new Error(errorData.error || 'Failed to create student')
      }

      setIsCreateDialogOpen(false)
      fetchStudents() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle student update
  const handleUpdateStudent = async (data: any) => {
    if (!editingStudent) return

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/students/${editingStudent.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          level: data.level,
          branch: data.branch,
          emergencyContact: data.emergencyContact,
          dateOfBirth: data.dateOfBirth,
          address: data.address
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update student')
      }

      setIsEditDialogOpen(false)
      setEditingStudent(null)
      fetchStudents() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle student deletion
  const handleDeleteStudent = async (studentId: string) => {
    if (!confirm('Are you sure you want to delete this student? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/students/${studentId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete student')
      }

      fetchStudents() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading students...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {showDroppedStudents ? 'Dropped Students Management' : 'Students Management'} - {currentBranch?.name}
          </h1>
          <p className="text-gray-600">
            {showDroppedStudents
              ? 'Contact and re-enroll dropped students - Lead-like functionality'
              : 'Unified student management with status tracking and payment monitoring'
            } for {currentBranch?.name}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={showDroppedStudents ? "default" : "outline"}
            onClick={() => setShowDroppedStudents(!showDroppedStudents)}
          >
            <UserX className="h-4 w-4 mr-2" />
            {showDroppedStudents ? 'Show Regular Students' : 'Show Dropped Students'}
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Student
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Student</DialogTitle>
                <DialogDescription>
                  Create a new student profile with their personal and academic information.
                </DialogDescription>
              </DialogHeader>
              <StudentForm
                onSubmit={handleCreateStudent}
                onCancel={() => setIsCreateDialogOpen(false)}
                isEditing={false}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStudents}</div>
            <p className="text-xs text-muted-foreground">
              All registered students
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Students</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeStudents}</div>
            <p className="text-xs text-muted-foreground">
              Currently enrolled
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unpaid Students</CardTitle>
            <CreditCard className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{unpaidStudents}</div>
            <p className="text-xs text-muted-foreground">
              Have pending payments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dropped Students</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{droppedStudents}</div>
            <p className="text-xs text-muted-foreground">
              Available for re-enrollment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paused Students</CardTitle>
            <Pause className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{pausedStudents}</div>
            <p className="text-xs text-muted-foreground">
              Temporarily inactive
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search and Filter Students</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by name, phone, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Statuses</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="DROPPED">Dropped</SelectItem>
                <SelectItem value="PAUSED">Paused</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={paymentFilter} onValueChange={setPaymentFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by payment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Payments</SelectItem>
                <SelectItem value="PAID">Paid</SelectItem>
                <SelectItem value="UNPAID">Unpaid</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Students Table */}
      <Card>
        <CardHeader>
          <CardTitle>Students ({filteredStudents.length})</CardTitle>
          <CardDescription>
            Unified student management with status and payment tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Current Group</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Payment Status</TableHead>
                <TableHead>Branch</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.map((student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          {getStatusIcon(student.status)}
                        </div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{student.user.name}</div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone className="h-3 w-3 mr-1" />
                          {student.user.phone}
                        </div>
                        {student.user.email && (
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {student.user.email}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(student.status)}>
                      {student.status}
                    </Badge>
                    {student.status === 'DROPPED' && student.droppedAt && (
                      <div className="text-xs text-gray-500 mt-1">
                        Dropped: {formatDate(new Date(student.droppedAt))}
                      </div>
                    )}
                    {student.status === 'PAUSED' && student.pausedAt && (
                      <div className="text-xs text-gray-500 mt-1">
                        Paused: {formatDate(new Date(student.pausedAt))}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {student.enrollments.length > 0 ? (
                      <div>
                        <div className="font-medium">{student.enrollments[0].group.name}</div>
                        <div className="text-sm text-gray-500">{student.enrollments[0].group.course.name}</div>
                        <div className="text-xs text-gray-500">Status: {student.enrollments[0].status}</div>
                      </div>
                    ) : (
                      <span className="text-gray-400">No enrollments</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge className={getLevelColor(student.level)}>
                      {student.level.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getPaymentStatusColor(student.paymentStatus)}>
                      {student.paymentStatus}
                    </Badge>
                    {student.paymentStatus === 'UNPAID' && student.unpaidAmount > 0 && (
                      <div className="text-xs text-red-600 mt-1">
                        Unpaid: ${student.unpaidAmount.toLocaleString()}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>{student.branch}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" onClick={() => setSelectedStudent(student)}>
                            View Details
                          </Button>
                        </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Student Details</DialogTitle>
                          <DialogDescription>
                            Complete information about {selectedStudent?.user.name}
                          </DialogDescription>
                        </DialogHeader>
                        {selectedStudent && (
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium">Name</label>
                                <p className="text-sm text-gray-600">{selectedStudent.user.name}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Phone</label>
                                <p className="text-sm text-gray-600">{selectedStudent.user.phone}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Email</label>
                                <p className="text-sm text-gray-600">{selectedStudent.user.email || 'Not provided'}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Level</label>
                                <p className="text-sm text-gray-600">{selectedStudent.level}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Branch</label>
                                <p className="text-sm text-gray-600">{selectedStudent.branch}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium">Emergency Contact</label>
                                <p className="text-sm text-gray-600">{selectedStudent.emergencyContact || 'Not provided'}</p>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium">Current Enrollments</label>
                              <div className="mt-2 space-y-2">
                                {selectedStudent.enrollments.map((enrollment) => (
                                  <div key={enrollment.id} className="flex items-center justify-between p-2 border rounded">
                                    <span>{enrollment.group.course.name} - {enrollment.group.name}</span>
                                    <Badge className={getStatusColor(enrollment.status)}>
                                      {enrollment.status}
                                    </Badge>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingStudent(student)
                        setIsEditDialogOpen(true)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteStudent(student.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredStudents.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No students found matching your search.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Student Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Student</DialogTitle>
            <DialogDescription>
              Update student information and academic details.
            </DialogDescription>
          </DialogHeader>
          {editingStudent && (
            <StudentForm
              initialData={{
                name: editingStudent.user.name,
                phone: editingStudent.user.phone,
                email: editingStudent.user.email || '',
                level: editingStudent.level as 'A1' | 'A2' | 'B1' | 'B2' | 'IELTS' | 'SAT' | 'MATH' | 'KIDS',
                branch: editingStudent.branch,
                emergencyContact: editingStudent.emergencyContact || '',
                dateOfBirth: editingStudent.dateOfBirth ? new Date(editingStudent.dateOfBirth).toISOString().split('T')[0] : '',
                address: editingStudent.address || ''
              }}
              onSubmit={handleUpdateStudent}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingStudent(null)
              }}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
