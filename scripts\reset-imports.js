const fs = require('fs');

// Function to reset imports back to @/ syntax
function resetImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Reset all relative imports back to @/ syntax
    let fixedContent = content
      // Reset any number of ../ back to @/
      .replace(/\.\.\/+components\/ui\//g, '@/components/ui/')
      .replace(/\.\.\/+components\//g, '@/components/')
      .replace(/\.\.\/+lib\//g, '@/lib/')
      .replace(/\.\.\/+hooks\//g, '@/hooks/')
      .replace(/\.\.\/+contexts\//g, '@/contexts/');
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Reset imports in: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error resetting ${filePath}:`, error.message);
    return false;
  }
}

// Files to reset
const filesToReset = [
  'app/(dashboard)/dashboard/page.tsx',
  'app/(dashboard)/dashboard/cabinets/page.tsx',
  'app/(dashboard)/dashboard/courses/page.tsx',
  'app/(dashboard)/dashboard/students/page.tsx',
  'app/(dashboard)/dashboard/teachers/page.tsx',
  'app/(dashboard)/dashboard/users/page.tsx',
  'app/(dashboard)/dashboard/leads/page.tsx',
  'app/(dashboard)/dashboard/groups/page.tsx',
  'app/(dashboard)/dashboard/communication/page.tsx',
  'app/(dashboard)/dashboard/unauthorized/page.tsx',
  'app/(dashboard)/dashboard/enrollments/page.tsx',
  'app/(dashboard)/dashboard/settings/page.tsx',
  'app/(dashboard)/dashboard/students/[id]/page.tsx',
  'app/(dashboard)/dashboard/groups/[id]/page.tsx',
  'app/(dashboard)/dashboard/teachers/[id]/page.tsx',
  'app/(dashboard)/dashboard/cabinets/[id]/page.tsx',
  'components/dashboard/sidebar.tsx',
  'components/dashboard/header.tsx',
  'components/dashboard/activity-feed.tsx'
];

console.log('🔄 Resetting all imports to @/ syntax...\n');

let resetCount = 0;
filesToReset.forEach(file => {
  if (fs.existsSync(file)) {
    if (resetImportsInFile(file)) {
      resetCount++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

console.log(`\n🎯 Summary: Reset imports in ${resetCount} files`);
