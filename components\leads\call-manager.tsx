'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Textarea } from '../../components/ui/textarea'
import { Label } from '../../components/ui/label'
import { Phone, PhoneOff, Clock, Mic, Square } from 'lucide-react'
import { formatTime } from '../../lib/utils'

interface CallManagerProps {
  leadId: string
  leadName: string
  leadPhone: string
  onCallComplete: () => void
  onError: (error: string) => void
}

export default function CallManager({
  leadId,
  leadName,
  leadPhone,
  onCallComplete,
  onError
}: CallManagerProps) {
  const [isCallActive, setIsCallActive] = useState(false)
  const [callDuration, setCallDuration] = useState(0)
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const maxCallDuration = 5 * 60 // 5 minutes in seconds

  const startTimer = () => {
    intervalRef.current = setInterval(() => {
      setCallDuration(prev => prev + 1)
    }, 1000)
  }

  const stopTimer = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }

  const handleEndCall = useCallback(async () => {
    setIsLoading(true)
    stopTimer()
    setIsRecording(false)

    try {
      // Generate a mock recording URL for demonstration
      const recordingUrl = isRecording ?
        `https://example.com/recordings/call-${leadId}-${Date.now()}.mp3` :
        undefined

      const response = await fetch(`/api/leads/${leadId}/call`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          duration: callDuration,
          notes,
          recordingUrl,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to end call')
      }

      setIsCallActive(false)
      setCallDuration(0)
      onCallComplete()
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to end call')
      // Restart timer if ending call failed
      if (isCallActive) {
        startTimer()
      }
    } finally {
      setIsLoading(false)
    }
  }, [leadId, callDuration, notes, onCallComplete, onError, isCallActive, isRecording])

  useEffect(() => {
    // Auto-end call after 5 minutes
    if (isCallActive && callDuration >= maxCallDuration) {
      handleEndCall()
    }
  }, [callDuration, isCallActive, maxCallDuration, handleEndCall])

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const handleStartCall = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/leads/${leadId}/call`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to start call')
      }

      setIsCallActive(true)
      setCallDuration(0)
      setIsRecording(true) // Auto-start recording
      startTimer()
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to start call')
    } finally {
      setIsLoading(false)
    }
  }

  const formatCallDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getCallStatusColor = () => {
    if (callDuration >= maxCallDuration - 30) return 'bg-red-100 text-red-800'
    if (callDuration >= maxCallDuration - 60) return 'bg-yellow-100 text-yellow-800'
    return 'bg-green-100 text-green-800'
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Call Management
          </div>
          {isCallActive && (
            <Badge className={getCallStatusColor()}>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                {isRecording ? 'Recording' : 'Active'}
              </div>
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <h3 className="font-semibold text-lg">{leadName}</h3>
          <p className="text-gray-600">{leadPhone}</p>
        </div>

        {isCallActive && (
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 text-2xl font-mono">
              <Clock className="h-6 w-6" />
              {formatCallDuration(callDuration)}
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Auto-end in {formatCallDuration(maxCallDuration - callDuration)}
            </p>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="call-notes">Call Notes</Label>
          <Textarea
            id="call-notes"
            placeholder="Add notes about the call..."
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            disabled={isLoading}
            rows={3}
          />
        </div>

        <div className="flex gap-2">
          {!isCallActive ? (
            <Button
              onClick={handleStartCall}
              disabled={isLoading}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Starting...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Start Call
                </div>
              )}
            </Button>
          ) : (
            <Button
              onClick={handleEndCall}
              disabled={isLoading}
              className="flex-1 bg-red-600 hover:bg-red-700"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Ending...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <PhoneOff className="h-4 w-4" />
                  End Call
                </div>
              )}
            </Button>
          )}
        </div>

        {isCallActive && (
          <div className="space-y-2">
            <div className="flex justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsRecording(!isRecording)}
                className={isRecording ? 'bg-red-50 border-red-200' : ''}
              >
                {isRecording ? (
                  <div className="flex items-center gap-2">
                    <Square className="h-3 w-3 text-red-600" />
                    Stop Recording
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Mic className="h-3 w-3" />
                    Start Recording
                  </div>
                )}
              </Button>
            </div>
            {isRecording && (
              <div className="text-xs text-gray-500 text-center">
                <div className="flex items-center justify-center gap-1">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                  Call is being recorded
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
