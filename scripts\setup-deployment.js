#!/usr/bin/env node

/**
 * Deployment Setup Script
 * 
 * Interactive script to set up all required environment variables
 * and configuration for deployment
 */

const fs = require('fs');
const readline = require('readline');
const crypto = require('crypto');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

class DeploymentSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.config = {};
    this.serverType = 'staff'; // Default
  }

  async setup() {
    try {
      log('\n🚀 DEPLOYMENT SETUP WIZARD', 'cyan');
      log('===========================', 'cyan');
      log('This wizard will help you configure your deployment.\n');

      await this.detectServerType();
      await this.setupBasicConfiguration();
      await this.setupDatabaseConfiguration();
      await this.setupAuthenticationConfiguration();
      await this.setupInterServerConfiguration();
      await this.setupOptionalConfiguration();
      await this.generateEnvironmentFiles();
      await this.displayInstructions();

    } catch (error) {
      logError(`Setup failed: ${error.message}`);
    } finally {
      this.rl.close();
    }
  }

  async question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  async detectServerType() {
    log('🔍 Detecting Server Type', 'blue');
    
    const currentServerType = process.env.SERVER_TYPE;
    if (currentServerType) {
      log(`   Current SERVER_TYPE: ${currentServerType}`);
      const useExisting = await this.question(`   Use existing server type? (y/n): `);
      if (useExisting.toLowerCase() === 'y') {
        this.serverType = currentServerType;
        this.config.SERVER_TYPE = currentServerType;
        logSuccess(`Using server type: ${currentServerType}`);
        return;
      }
    }

    log('\n   Available server types:');
    log('   1. admin  - Full access admin server');
    log('   2. staff  - Limited access staff server');
    
    const choice = await this.question('   Select server type (1-2): ');
    
    switch (choice) {
      case '1':
        this.serverType = 'admin';
        break;
      case '2':
        this.serverType = 'staff';
        break;
      default:
        this.serverType = 'staff';
        logWarning('Invalid choice, defaulting to staff server');
    }
    
    this.config.SERVER_TYPE = this.serverType;
    logSuccess(`Server type set to: ${this.serverType}`);
  }

  async setupBasicConfiguration() {
    log('\n⚙️ Basic Configuration', 'blue');
    
    // App Name
    const defaultAppName = `Innovative Centre${this.serverType === 'staff' ? ' - Staff Portal' : ' - Admin Portal'}`;
    const appName = await this.question(`   App Name (${defaultAppName}): `);
    this.config.APP_NAME = appName || defaultAppName;
    
    // Environment
    this.config.APP_ENV = 'production';
    this.config.NODE_ENV = 'production';
    this.config.PRISMA_GENERATE_DATAPROXY = 'true';
    
    logSuccess('Basic configuration completed');
  }

  async setupDatabaseConfiguration() {
    log('\n🗄️ Database Configuration', 'blue');
    
    const existingDbUrl = process.env.DATABASE_URL;
    if (existingDbUrl) {
      log(`   Current DATABASE_URL: ${existingDbUrl.substring(0, 30)}...`);
      const useExisting = await this.question('   Use existing database URL? (y/n): ');
      if (useExisting.toLowerCase() === 'y') {
        this.config.DATABASE_URL = existingDbUrl;
        logSuccess('Using existing database URL');
        return;
      }
    }
    
    log('\n   Please provide your production database URL.');
    log('   Format: postgresql://username:password@host:port/database?sslmode=require');
    log('   Example: postgresql://user:<EMAIL>/neondb?sslmode=require');
    
    const databaseUrl = await this.question('   Database URL: ');
    
    if (!databaseUrl) {
      logError('Database URL is required');
      throw new Error('Database URL is required');
    }
    
    if (!databaseUrl.startsWith('postgresql://') && !databaseUrl.startsWith('postgres://')) {
      logWarning('Database URL should start with postgresql:// or postgres://');
    }
    
    this.config.DATABASE_URL = databaseUrl;
    logSuccess('Database configuration completed');
  }

  async setupAuthenticationConfiguration() {
    log('\n🔐 Authentication Configuration', 'blue');
    
    // NEXTAUTH_SECRET
    const existingSecret = process.env.NEXTAUTH_SECRET;
    if (existingSecret && existingSecret.length >= 32) {
      const useExisting = await this.question('   Use existing NEXTAUTH_SECRET? (y/n): ');
      if (useExisting.toLowerCase() === 'y') {
        this.config.NEXTAUTH_SECRET = existingSecret;
        logSuccess('Using existing NEXTAUTH_SECRET');
      } else {
        this.config.NEXTAUTH_SECRET = this.generateSecureSecret();
        logSuccess('Generated new NEXTAUTH_SECRET');
      }
    } else {
      this.config.NEXTAUTH_SECRET = this.generateSecureSecret();
      logSuccess('Generated NEXTAUTH_SECRET');
    }
    
    // NEXTAUTH_URL
    log('\n   Please provide your deployment URL.');
    log('   This will be your Vercel app URL (e.g., https://your-app.vercel.app)');
    
    const nextAuthUrl = await this.question('   Deployment URL: ');
    
    if (!nextAuthUrl) {
      logWarning('NEXTAUTH_URL not set - you will need to set this after deployment');
      this.config.NEXTAUTH_URL = 'https://your-app.vercel.app';
    } else {
      if (!nextAuthUrl.startsWith('https://')) {
        logWarning('URL should start with https:// for production');
      }
      this.config.NEXTAUTH_URL = nextAuthUrl;
    }
    
    logSuccess('Authentication configuration completed');
  }

  async setupInterServerConfiguration() {
    log('\n🔗 Inter-Server Communication', 'blue');
    
    // Generate inter-server secret
    const existingInterSecret = process.env.INTER_SERVER_SECRET;
    if (existingInterSecret) {
      const useExisting = await this.question('   Use existing INTER_SERVER_SECRET? (y/n): ');
      if (useExisting.toLowerCase() === 'y') {
        this.config.INTER_SERVER_SECRET = existingInterSecret;
        logSuccess('Using existing INTER_SERVER_SECRET');
      } else {
        this.config.INTER_SERVER_SECRET = this.generateSecureSecret();
        logSuccess('Generated new INTER_SERVER_SECRET');
      }
    } else {
      this.config.INTER_SERVER_SECRET = this.generateSecureSecret();
      logSuccess('Generated INTER_SERVER_SECRET');
    }
    
    // Server URLs
    log('\n   Please provide the URLs for both servers:');
    
    const adminUrl = await this.question('   Admin Server URL (https://inno-crm-admin.vercel.app): ');
    this.config.ADMIN_SERVER_URL = adminUrl || 'https://inno-crm-admin.vercel.app';
    
    const staffUrl = await this.question('   Staff Server URL (https://inno-crm-staff.vercel.app): ');
    this.config.STAFF_SERVER_URL = staffUrl || 'https://inno-crm-staff.vercel.app';
    
    // Set APP_URL based on server type
    this.config.APP_URL = this.serverType === 'admin' ? this.config.ADMIN_SERVER_URL : this.config.STAFF_SERVER_URL;
    
    logSuccess('Inter-server configuration completed');
  }

  async setupOptionalConfiguration() {
    log('\n📧 Optional Services Configuration', 'blue');
    
    const setupOptional = await this.question('   Configure SMS/Email services? (y/n): ');
    
    if (setupOptional.toLowerCase() === 'y') {
      // SMS Configuration
      log('\n   SMS Configuration (Eskiz):');
      const smsApiKey = await this.question('   SMS API Key (optional): ');
      if (smsApiKey) {
        this.config.SMS_PROVIDER = 'eskiz';
        this.config.SMS_API_KEY = smsApiKey;
      }
      
      // Email Configuration
      log('\n   Email Configuration (Gmail):');
      const emailUser = await this.question('   Email address (optional): ');
      if (emailUser) {
        const emailPassword = await this.question('   App password (optional): ');
        this.config.EMAIL_PROVIDER = 'gmail';
        this.config.EMAIL_USER = emailUser;
        this.config.EMAIL_PASSWORD = emailPassword;
      }
    }
    
    logSuccess('Optional configuration completed');
  }

  generateSecureSecret() {
    return crypto.randomBytes(32).toString('hex');
  }

  async generateEnvironmentFiles() {
    log('\n📝 Generating Environment Files', 'blue');
    
    // Generate .env.production
    const envContent = Object.entries(this.config)
      .map(([key, value]) => `${key}="${value}"`)
      .join('\n');
    
    fs.writeFileSync('.env.production', envContent);
    logSuccess('Generated .env.production');
    
    // Generate Vercel environment variables script
    const vercelEnvScript = this.generateVercelEnvScript();
    fs.writeFileSync('scripts/set-vercel-env.sh', vercelEnvScript);
    fs.chmodSync('scripts/set-vercel-env.sh', '755');
    logSuccess('Generated Vercel environment script');
    
    // Generate environment variables list for manual setup
    const envList = this.generateEnvironmentList();
    fs.writeFileSync('DEPLOYMENT_ENV_VARS.md', envList);
    logSuccess('Generated environment variables documentation');
  }

  generateVercelEnvScript() {
    const commands = Object.entries(this.config)
      .map(([key, value]) => `vercel env add ${key} "${value}" production`)
      .join('\n');
    
    return `#!/bin/bash
# Vercel Environment Variables Setup Script
# Run this script to set all environment variables in Vercel

echo "Setting up Vercel environment variables..."

${commands}

echo "Environment variables setup completed!"
echo "You can now deploy with: vercel --prod"
`;
  }

  generateEnvironmentList() {
    const envList = Object.entries(this.config)
      .map(([key, value]) => `- **${key}**: \`${key === 'DATABASE_URL' || key.includes('SECRET') || key.includes('PASSWORD') ? '[HIDDEN]' : value}\``)
      .join('\n');
    
    return `# Deployment Environment Variables

## Required Environment Variables

${envList}

## Setup Instructions

### Option 1: Use Vercel CLI Script
\`\`\`bash
chmod +x scripts/set-vercel-env.sh
./scripts/set-vercel-env.sh
\`\`\`

### Option 2: Manual Setup in Vercel Dashboard
1. Go to your Vercel project dashboard
2. Navigate to Settings → Environment Variables
3. Add each variable listed above
4. Set the environment to "Production"

### Option 3: Use Vercel CLI Manually
\`\`\`bash
${Object.entries(this.config)
  .map(([key, value]) => `vercel env add ${key} "${value}" production`)
  .join('\n')}
\`\`\`

## Important Notes

- Keep your secrets secure and never commit them to version control
- The NEXTAUTH_URL should match your actual deployment URL
- Both servers should use the same INTER_SERVER_SECRET
- Database URL should include \`sslmode=require\` for production
`;
  }

  async displayInstructions() {
    log('\n🎯 DEPLOYMENT INSTRUCTIONS', 'cyan');
    log('==========================', 'cyan');
    
    log('\n✅ Configuration completed! Next steps:');
    log('\n1. 📁 Files generated:');
    log('   • .env.production (local environment file)');
    log('   • scripts/set-vercel-env.sh (Vercel setup script)');
    log('   • DEPLOYMENT_ENV_VARS.md (documentation)');
    
    log('\n2. 🚀 Deploy to Vercel:');
    log('   Option A - Automated:');
    log('   npm run deploy:auto');
    log('');
    log('   Option B - Manual:');
    log('   chmod +x scripts/set-vercel-env.sh');
    log('   ./scripts/set-vercel-env.sh');
    log('   vercel --prod');
    
    log('\n3. ✅ Verify deployment:');
    log('   npm run verify:deployment');
    
    log('\n4. 🔧 Important reminders:');
    log('   • Update NEXTAUTH_URL with your actual deployment URL');
    log('   • Ensure both admin and staff servers use the same INTER_SERVER_SECRET');
    log('   • Test all functionality after deployment');
    
    logSuccess('\nSetup completed! You are ready to deploy.');
  }
}

// Run setup
if (require.main === module) {
  const setup = new DeploymentSetup();
  setup.setup().catch(error => {
    console.error('Setup failed:', error.message);
    process.exit(1);
  });
}

module.exports = DeploymentSetup;
