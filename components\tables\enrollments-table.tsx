"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "../../components/ui/button"
import { Input } from "../../components/ui/input"
import { Badge } from "../../components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../components/ui/dialog"
import { EnrollmentForm } from "../../components/forms/enrollment-form"
import { Search, Plus, Edit, Trash2, Download, Filter, Calendar } from "lucide-react"

interface Enrollment {
  id: string
  studentId: string
  groupId: string
  student: {
    user: {
      name: string
      phone: string
    }
    level: string
  }
  group: {
    name: string
    course: {
      name: string
      level: string
      price: number
    }
    teacher: {
      user: {
        name: string
      }
    }
    branch: string
    schedule: string
  }
  status: string
  startDate: string
  endDate: string | null
  createdAt: string
  updatedAt: string
}

interface EnrollmentsTableProps {
  initialData?: Enrollment[]
}

export function EnrollmentsTable({ initialData = [] }: EnrollmentsTableProps) {
  const [enrollments, setEnrollments] = useState<Enrollment[]>(initialData)
  const [filteredEnrollments, setFilteredEnrollments] = useState<Enrollment[]>(initialData)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [groupFilter, setGroupFilter] = useState("")
  const [branchFilter, setBranchFilter] = useState("")
  const [loading, setLoading] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingEnrollment, setEditingEnrollment] = useState<Enrollment | null>(null)

  // Fetch enrollments data
  const fetchEnrollments = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/enrollments")
      if (response.ok) {
        const data = await response.json()
        setEnrollments(data)
        setFilteredEnrollments(data)
      }
    } catch (error) {
      console.error("Error fetching enrollments:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (initialData.length === 0) {
      fetchEnrollments()
    }
  }, [initialData])

  // Filter enrollments based on search and filters
  useEffect(() => {
    let filtered = enrollments

    if (searchTerm) {
      filtered = filtered.filter(
        (enrollment) =>
          enrollment.student.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          enrollment.student.user.phone.includes(searchTerm) ||
          enrollment.group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          enrollment.group.course.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter) {
      filtered = filtered.filter((enrollment) => enrollment.status === statusFilter)
    }

    if (groupFilter) {
      filtered = filtered.filter((enrollment) => enrollment.group.name === groupFilter)
    }

    if (branchFilter) {
      filtered = filtered.filter((enrollment) => enrollment.group.branch === branchFilter)
    }

    setFilteredEnrollments(filtered)
  }, [enrollments, searchTerm, statusFilter, groupFilter, branchFilter])

  // Get unique values for filters
  const uniqueStatuses = [...new Set(enrollments.map((enrollment) => enrollment.status))]
  const uniqueGroups = [...new Set(enrollments.map((enrollment) => enrollment.group.name))]
  const uniqueBranches = [...new Set(enrollments.map((enrollment) => enrollment.group.branch))]

  // Handle enrollment deletion
  const handleDelete = async (enrollmentId: string) => {
    if (!confirm("Are you sure you want to delete this enrollment?")) return

    try {
      const response = await fetch(`/api/enrollments/${enrollmentId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setEnrollments(enrollments.filter((enrollment) => enrollment.id !== enrollmentId))
      } else {
        alert("Failed to delete enrollment")
      }
    } catch (error) {
      console.error("Error deleting enrollment:", error)
      alert("Error deleting enrollment")
    }
  }

  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    try {
      // Submit the enrollment data to the API
      const response = await fetch('/api/enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error('Failed to save enrollment')
      }

      setIsCreateDialogOpen(false)
      setEditingEnrollment(null)
      fetchEnrollments()
    } catch (error) {
      console.error('Error saving enrollment:', error)
      throw error
    }
  }

  // Export to CSV
  const exportToCSV = () => {
    const headers = [
      "Student Name",
      "Phone",
      "Student Level",
      "Group",
      "Course",
      "Course Level",
      "Teacher",
      "Branch",
      "Status",
      "Start Date",
      "End Date",
      "Price",
    ]
    const csvData = filteredEnrollments.map((enrollment) => [
      enrollment.student.user.name,
      enrollment.student.user.phone,
      enrollment.student.level,
      enrollment.group.name,
      enrollment.group.course.name,
      enrollment.group.course.level,
      enrollment.group.teacher.user.name,
      enrollment.group.branch,
      enrollment.status,
      new Date(enrollment.startDate).toLocaleDateString(),
      enrollment.endDate ? new Date(enrollment.endDate).toLocaleDateString() : "",
      enrollment.group.course.price,
    ])

    const csvContent = [headers, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `enrollments-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "default"
      case "COMPLETED":
        return "secondary"
      case "DROPPED":
        return "destructive"
      case "SUSPENDED":
        return "outline"
      default:
        return "secondary"
    }
  }

  // Calculate statistics
  const totalEnrollments = filteredEnrollments.length
  const activeEnrollments = filteredEnrollments.filter((e) => e.status === "ACTIVE").length
  const completedEnrollments = filteredEnrollments.filter((e) => e.status === "COMPLETED").length
  const droppedEnrollments = filteredEnrollments.filter((e) => e.status === "DROPPED").length
  const totalRevenue = filteredEnrollments.reduce((sum, enrollment) => sum + enrollment.group.course.price, 0)

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-2xl font-bold">Enrollments</h2>
        <div className="flex gap-2">
          <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Enrollment
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Enrollment</DialogTitle>
              </DialogHeader>
              <EnrollmentForm onSubmit={handleFormSubmit} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-blue-600">Total</h3>
          <p className="text-2xl font-bold text-blue-900">{totalEnrollments}</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-green-600">Active</h3>
          <p className="text-2xl font-bold text-green-900">{activeEnrollments}</p>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-600">Completed</h3>
          <p className="text-2xl font-bold text-gray-900">{completedEnrollments}</p>
        </div>
        <div className="bg-red-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-red-600">Dropped</h3>
          <p className="text-2xl font-bold text-red-900">{droppedEnrollments}</p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-purple-600">Total Revenue</h3>
          <p className="text-2xl font-bold text-purple-900">
            {totalRevenue.toLocaleString()} UZS
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search enrollments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Statuses</option>
          {uniqueStatuses.map((status) => (
            <option key={status} value={status}>
              {status}
            </option>
          ))}
        </select>
        <select
          value={groupFilter}
          onChange={(e) => setGroupFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Groups</option>
          {uniqueGroups.map((group) => (
            <option key={group} value={group}>
              {group}
            </option>
          ))}
        </select>
        <select
          value={branchFilter}
          onChange={(e) => setBranchFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Branches</option>
          {uniqueBranches.map((branch) => (
            <option key={branch} value={branch}>
              {branch}
            </option>
          ))}
        </select>
        {(statusFilter || groupFilter || branchFilter || searchTerm) && (
          <Button
            variant="outline"
            onClick={() => {
              setStatusFilter("")
              setGroupFilter("")
              setBranchFilter("")
              setSearchTerm("")
            }}
          >
            Clear Filters
          </Button>
        )}
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Group</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Teacher</TableHead>
              <TableHead>Branch</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>End Date</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-8">
                  Loading enrollments...
                </TableCell>
              </TableRow>
            ) : filteredEnrollments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-8">
                  No enrollments found
                </TableCell>
              </TableRow>
            ) : (
              filteredEnrollments.map((enrollment) => (
                <TableRow key={enrollment.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{enrollment.student.user.name}</div>
                      <div className="text-sm text-gray-500">
                        {enrollment.student.user.phone}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {enrollment.student.level}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{enrollment.group.name}</Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{enrollment.group.course.name}</div>
                      <div className="text-sm text-gray-500">
                        Level: {enrollment.group.course.level}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{enrollment.group.teacher.user.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{enrollment.group.branch}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(enrollment.status)}>
                      {enrollment.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(enrollment.startDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {enrollment.endDate
                      ? new Date(enrollment.endDate).toLocaleDateString()
                      : "Ongoing"}
                  </TableCell>
                  <TableCell className="font-medium">
                    {enrollment.group.course.price.toLocaleString()} UZS
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingEnrollment(enrollment)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Edit Enrollment</DialogTitle>
                          </DialogHeader>
                          {editingEnrollment && (
                            <EnrollmentForm
                              initialData={{
                                studentId: editingEnrollment.studentId,
                                groupId: editingEnrollment.groupId,
                                status: editingEnrollment.status as any,
                                startDate: editingEnrollment.startDate,
                                endDate: editingEnrollment.endDate || undefined,
                              }}
                              onSubmit={handleFormSubmit}
                              isEditing={true}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(enrollment.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Results count */}
      <div className="text-sm text-gray-500">
        Showing {filteredEnrollments.length} of {enrollments.length} enrollments
      </div>
    </div>
  )
}
