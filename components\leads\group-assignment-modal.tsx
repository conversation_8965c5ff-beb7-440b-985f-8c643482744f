'use client'

import { useState, useEffect, useCallback } from 'react'
import { useBranch } from '../../contexts/branch-context'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../components/ui/dialog'
import { <PERSON><PERSON> } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { Card, CardContent } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Textarea } from '../../components/ui/textarea'
import { Search, Users, User, Calendar, Clock, MapPin } from 'lucide-react'

// Helper functions for teacher tier styling
const getTeacherTierStyle = (tier: string) => {
  switch (tier) {
    case 'A_LEVEL':
      return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold'
    case 'B_LEVEL':
      return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium'
    case 'C_LEVEL':
      return 'bg-gradient-to-r from-green-400 to-green-600 text-white'
    case 'NEW':
      return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getTeacherTierLabel = (tier: string) => {
  switch (tier) {
    case 'A_LEVEL':
      return 'A-Level'
    case 'B_LEVEL':
      return 'B-Level'
    case 'C_LEVEL':
      return 'C-Level'
    case 'NEW':
      return 'New'
    default:
      return 'Unknown'
  }
}

interface Group {
  id: string
  name: string
  capacity: number
  schedule: string
  room?: string
  branch: string
  startDate: string
  endDate: string
  course: {
    name: string
    level: string
  }
  teacher: {
    id: string
    tier: string
    subject: string
    user: {
      name: string
    }
  }
  _count: {
    enrollments: number
  }
}

interface SlotAnalysis {
  slotKey: string
  courseLevel: string
  days: string
  time: string
  tierUtilization: {
    tier: string
    utilizationRate: number
    groupCount: number
    availableGroups: number
  }[]
  totalAvailable: number
}

interface GroupAssignmentModalProps {
  isOpen: boolean
  onClose: () => void
  leadId: string
  leadName: string
  onAssignmentComplete: () => void
  onError: (error: string) => void
}

export default function GroupAssignmentModal({
  isOpen,
  onClose,
  leadId,
  leadName,
  onAssignmentComplete,
  onError
}: GroupAssignmentModalProps) {
  const { currentBranch } = useBranch()
  const [groups, setGroups] = useState<Group[]>([])
  const [filteredGroups, setFilteredGroups] = useState<Group[]>([])
  const [slotAnalysis, setSlotAnalysis] = useState<SlotAnalysis[]>([])
  const [selectedGroupId, setSelectedGroupId] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [teacherFilter, setTeacherFilter] = useState('')
  const [levelFilter, setLevelFilter] = useState('')
  const [teacherTierFilter, setTeacherTierFilter] = useState('')
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isAssigning, setIsAssigning] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  // Get unique teachers and levels for filters
  const uniqueTeachers = Array.from(new Set(groups.map(g => g.teacher.user.name))).sort()
  const uniqueLevels = Array.from(new Set(groups.map(g => g.course.level))).sort()

  const fetchAvailableGroups = useCallback(async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/leads/${leadId}/assign-group?branch=${currentBranch.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch available groups')
      }
      const data = await response.json()
      setGroups(data.groups)
      setSlotAnalysis(data.slotAnalysis || [])
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to fetch groups')
    } finally {
      setIsLoading(false)
    }
  }, [leadId, currentBranch.id, onError])

  const filterGroups = useCallback(() => {
    let filtered = groups

    if (searchTerm) {
      filtered = filtered.filter(group =>
        group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        group.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (teacherFilter) {
      filtered = filtered.filter(group => group.teacher.user.name === teacherFilter)
    }

    if (levelFilter) {
      filtered = filtered.filter(group => group.course.level === levelFilter)
    }

    if (teacherTierFilter) {
      filtered = filtered.filter(group => group.teacher.tier === teacherTierFilter)
    }

    setFilteredGroups(filtered)
  }, [groups, searchTerm, teacherFilter, levelFilter, teacherTierFilter])

  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    if (isOpen && isMounted) {
      fetchAvailableGroups()
    }
  }, [isOpen, fetchAvailableGroups, isMounted])

  useEffect(() => {
    if (isMounted) {
      filterGroups()
    }
  }, [groups, searchTerm, teacherFilter, levelFilter, teacherTierFilter, filterGroups, isMounted])

  const handleAssignGroup = async () => {
    if (!selectedGroupId) return

    setIsAssigning(true)
    try {
      const response = await fetch(`/api/leads/${leadId}/assign-group`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          groupId: selectedGroupId,
          notes,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to assign group')
      }

      onAssignmentComplete()
      onClose()
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to assign group')
    } finally {
      setIsAssigning(false)
    }
  }

  const formatSchedule = (schedule: string) => {
    try {
      const parsed = JSON.parse(schedule)
      return Array.isArray(parsed) ? parsed.join(', ') : schedule
    } catch {
      return schedule
    }
  }

  const getAvailabilityColor = (current: number, capacity: number) => {
    const percentage = (current / capacity) * 100
    if (percentage >= 90) return 'bg-red-100 text-red-800'
    if (percentage >= 70) return 'bg-yellow-100 text-yellow-800'
    return 'bg-green-100 text-green-800'
  }

  // Prevent hydration mismatch by only rendering on client
  if (!isMounted) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Assign Group to {leadName}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <Label htmlFor="search">Search Groups</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search by name, course, teacher..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="teacher-filter">Filter by Teacher</Label>
              <Select value={teacherFilter || "all"} onValueChange={(value) => setTeacherFilter(value === "all" ? "" : value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All teachers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All teachers</SelectItem>
                  {uniqueTeachers.map(teacher => (
                    <SelectItem key={teacher} value={teacher}>{teacher}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="level-filter">Filter by Level</Label>
              <Select value={levelFilter || "all"} onValueChange={(value) => setLevelFilter(value === "all" ? "" : value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All levels</SelectItem>
                  {uniqueLevels.map(level => (
                    <SelectItem key={level} value={level}>{level}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="tier-filter">Filter by Teacher Tier</Label>
              <Select value={teacherTierFilter || "all"} onValueChange={(value) => setTeacherTierFilter(value === "all" ? "" : value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All tiers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All tiers</SelectItem>
                  <SelectItem value="A_LEVEL">A-Level Teachers</SelectItem>
                  <SelectItem value="B_LEVEL">B-Level Teachers</SelectItem>
                  <SelectItem value="C_LEVEL">C-Level Teachers</SelectItem>
                  <SelectItem value="NEW">New Teachers</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('')
                  setTeacherFilter('')
                  setLevelFilter('')
                  setTeacherTierFilter('')
                }}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Groups List */}
          <div className="space-y-3">
            <h3 className="font-medium">
              Available Groups ({filteredGroups.length})
            </h3>
            
            {isLoading ? (
              <div className="text-center py-8">
                <div className="inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
                <p className="mt-2 text-gray-600">Loading groups...</p>
              </div>
            ) : filteredGroups.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No available groups found
              </div>
            ) : (
              <div className="grid gap-3 max-h-60 overflow-y-auto">
                {filteredGroups.map((group) => (
                  <Card
                    key={group.id}
                    className={`cursor-pointer transition-colors ${
                      selectedGroupId === group.id
                        ? 'ring-2 ring-blue-500 bg-blue-50'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedGroupId(group.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="space-y-2 flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{group.name}</h4>
                            <Badge variant="outline">
                              {group.course.name} - {group.course.level}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {group.teacher.user.name}
                              </div>
                              <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle(group.teacher.tier || 'NEW')}`}>
                                {getTeacherTierLabel(group.teacher.tier || 'NEW')}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {group.room || 'TBA'} - {group.branch}
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatSchedule(group.schedule)}
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(group.startDate).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        
                        <Badge className={getAvailabilityColor(group._count.enrollments, group.capacity)}>
                          <Users className="h-3 w-3 mr-1" />
                          {group._count.enrollments}/{group.capacity}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="assignment-notes">Assignment Notes (Optional)</Label>
            <Textarea
              id="assignment-notes"
              placeholder="Add any notes about this group assignment..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleAssignGroup}
              disabled={!selectedGroupId || isAssigning}
            >
              {isAssigning ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Assigning...
                </div>
              ) : (
                'Assign Group'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
