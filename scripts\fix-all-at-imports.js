const fs = require('fs');
const path = require('path');

// Function to calculate the correct relative path depth
function getRelativeDepth(filePath) {
  // Manual mapping for correct paths based on known file locations
  const normalizedPath = filePath.replace(/\\/g, '/');

  // Dashboard main pages (4 levels up)
  if (normalizedPath.match(/app\/\(dashboard\)\/dashboard\/[^\/]+\/page\.tsx$/)) {
    return '../../../../';
  }

  // Dashboard nested pages (5 levels up)
  if (normalizedPath.match(/app\/\(dashboard\)\/dashboard\/[^\/]+\/\[id\]\/page\.tsx$/)) {
    return '../../../../../';
  }

  // Dashboard root page (3 levels up)
  if (normalizedPath === 'app/(dashboard)/dashboard/page.tsx') {
    return '../../../';
  }

  // Student pages (5 levels up)
  if (normalizedPath.match(/app\/\(dashboard\)\/dashboard\/student\/[^\/]+\/page\.tsx$/)) {
    return '../../../../../';
  }

  // Communication sub-pages (6 levels up)
  if (normalizedPath.match(/app\/\(dashboard\)\/dashboard\/communication\/[^\/]+\/page\.tsx$/)) {
    return '../../../../../';
  }

  // Layout files (3 levels up)
  if (normalizedPath === 'app/(dashboard)/layout.tsx') {
    return '../../../';
  }

  // Root layout (1 level up)
  if (normalizedPath === 'app/layout.tsx') {
    return '../';
  }

  // Auth pages (2 levels up)
  if (normalizedPath.match(/app\/auth\/[^\/]+\/page\.tsx$/)) {
    return '../../../';
  }

  // Root page (1 level up)
  if (normalizedPath === 'app/page.tsx') {
    return '../';
  }

  // API routes - calculate based on depth
  if (normalizedPath.startsWith('app/api/')) {
    const apiParts = normalizedPath.split('/');
    const levelsAfterApi = apiParts.length - 3; // -3 for app, api, and filename
    return '../'.repeat(levelsAfterApi + 2); // +2 to get out of app/api
  }

  // Components (2 levels up from components directory)
  if (normalizedPath.startsWith('components/')) {
    const componentParts = normalizedPath.split('/');
    const levelsAfterComponents = componentParts.length - 2; // -2 for components and filename
    return '../'.repeat(levelsAfterComponents + 1); // +1 to get out of components
  }

  // Default fallback
  return '../../../';
}

// Function to fix imports in a file
function fixImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = getRelativeDepth(filePath);
    
    // Replace all @/ imports with relative paths
    let fixedContent = content
      .replace(/@\/components\/ui\//g, `${relativePath}components/ui/`)
      .replace(/@\/components\//g, `${relativePath}components/`)
      .replace(/@\/lib\//g, `${relativePath}lib/`)
      .replace(/@\/hooks\//g, `${relativePath}hooks/`)
      .replace(/@\/contexts\//g, `${relativePath}contexts/`)
      .replace(/@\/types\//g, `${relativePath}types/`)
      .replace(/@\/utils\//g, `${relativePath}utils/`);
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed imports in: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to recursively find all TypeScript/React files
function findTsxFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (!['node_modules', '.next', '.git'].includes(file)) {
        findTsxFiles(filePath, fileList);
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

console.log('🔧 Finding all TypeScript/React files with @/ imports...\n');

// Find all files in app and components directories
const appFiles = findTsxFiles('app');
const componentFiles = findTsxFiles('components');
const allFiles = [...appFiles, ...componentFiles];

console.log(`Found ${allFiles.length} TypeScript/React files\n`);

let fixedCount = 0;
allFiles.forEach(filePath => {
  if (fixImportsInFile(filePath)) {
    fixedCount++;
  }
});

console.log(`\n🎯 Summary: Fixed imports in ${fixedCount} files out of ${allFiles.length} total files`);
