"use client"

import { useState, useEffect, useCallback } from "react"
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadialBar<PERSON>hart,
  RadialBar,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts"
import { Card } from "../../components/ui/card"
import { Button } from "../../components/ui/button"
import { Badge } from "../../components/ui/badge"
import { TrendingUp, Award, BookOpen, Target, Users } from "lucide-react"

interface ProgressData {
  month: string
  levelUps: number
  completions: number
  averageScore: number
  totalAssessments: number
}

interface LevelDistribution {
  level: string
  students: number
  percentage: number
  color: string
}

interface CourseCompletion {
  course: string
  completed: number
  inProgress: number
  completionRate: number
}

interface StudentProgressChartProps {
  className?: string
}

export function StudentProgressChart({ className }: StudentProgressChartProps) {
  const [progressData, setProgressData] = useState<ProgressData[]>([])
  const [levelData, setLevelData] = useState<LevelDistribution[]>([])
  const [courseData, setCourseData] = useState<CourseCompletion[]>([])
  const [chartType, setChartType] = useState<"line" | "area" | "bar">("area")
  const [timeRange, setTimeRange] = useState("12months")
  const [loading, setLoading] = useState(true)

  // Fetch student progress analytics data
  const fetchProgressData = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/analytics/progress?range=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setProgressData(data.monthlyProgress || [])
        setLevelData(data.levelDistribution || [])
        setCourseData(data.courseCompletion || [])
      }
    } catch (error) {
      console.error("Error fetching progress data:", error)
      // Mock data for development
      setProgressData([
        { month: "Jan", levelUps: 12, completions: 8, averageScore: 78.5, totalAssessments: 45 },
        { month: "Feb", levelUps: 15, completions: 12, averageScore: 81.2, totalAssessments: 52 },
        { month: "Mar", levelUps: 18, completions: 15, averageScore: 79.8, totalAssessments: 48 },
        { month: "Apr", levelUps: 22, completions: 18, averageScore: 83.1, totalAssessments: 58 },
        { month: "May", levelUps: 19, completions: 16, averageScore: 80.7, totalAssessments: 55 },
        { month: "Jun", levelUps: 25, completions: 22, averageScore: 85.3, totalAssessments: 62 },
        { month: "Jul", levelUps: 21, completions: 19, averageScore: 82.9, totalAssessments: 59 },
        { month: "Aug", levelUps: 28, completions: 25, averageScore: 86.4, totalAssessments: 68 },
        { month: "Sep", levelUps: 24, completions: 21, averageScore: 84.1, totalAssessments: 64 },
        { month: "Oct", levelUps: 30, completions: 28, averageScore: 87.8, totalAssessments: 72 },
        { month: "Nov", levelUps: 26, completions: 24, averageScore: 85.6, totalAssessments: 69 },
        { month: "Dec", levelUps: 32, completions: 30, averageScore: 88.9, totalAssessments: 75 },
      ])
      setLevelData([
        { level: "A1", students: 156, percentage: 25.2, color: "#FF6B6B" },
        { level: "A2", students: 134, percentage: 21.6, color: "#4ECDC4" },
        { level: "B1", students: 128, percentage: 20.6, color: "#45B7D1" },
        { level: "B2", students: 89, percentage: 14.4, color: "#96CEB4" },
        { level: "C1", students: 67, percentage: 10.8, color: "#FFEAA7" },
        { level: "C2", students: 45, percentage: 7.3, color: "#DDA0DD" },
      ])
      setCourseData([
        { course: "General English A1", completed: 45, inProgress: 89, completionRate: 33.6 },
        { course: "General English A2", completed: 38, inProgress: 76, completionRate: 33.3 },
        { course: "General English B1", completed: 42, inProgress: 68, completionRate: 38.2 },
        { course: "IELTS 6.0", completed: 28, inProgress: 45, completionRate: 38.4 },
        { course: "General English B2", completed: 25, inProgress: 38, completionRate: 39.7 },
        { course: "IELTS 6.5", completed: 18, inProgress: 28, completionRate: 39.1 },
      ])
    } finally {
      setLoading(false)
    }
  }, [timeRange])

  useEffect(() => {
    fetchProgressData()
  }, [fetchProgressData])

  // Calculate metrics
  const totalLevelUps = progressData.reduce((sum, item) => sum + item.levelUps, 0)
  const totalCompletions = progressData.reduce((sum, item) => sum + item.completions, 0)
  const averageScore = progressData.length > 0
    ? (progressData.reduce((sum, item) => sum + item.averageScore, 0) / progressData.length).toFixed(1)
    : "0"
  
  const totalStudents = levelData.reduce((sum, level) => sum + level.students, 0)
  const overallCompletionRate = courseData.length > 0
    ? (courseData.reduce((sum, course) => sum + course.completionRate, 0) / courseData.length).toFixed(1)
    : "0"

  // Get level progression trend
  const currentMonth = progressData[progressData.length - 1]
  const previousMonth = progressData[progressData.length - 2]
  const progressTrend = previousMonth && currentMonth
    ? ((currentMonth.levelUps - previousMonth.levelUps) / previousMonth.levelUps * 100).toFixed(1)
    : "0"

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">Student Progress Analytics</h3>
          <p className="text-sm text-gray-500">Track student advancement and course completion rates</p>
        </div>
        <div className="flex gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="6months">Last 6 Months</option>
            <option value="12months">Last 12 Months</option>
            <option value="24months">Last 24 Months</option>
          </select>
          <Button
            variant={chartType === "area" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("area")}
          >
            Area
          </Button>
          <Button
            variant={chartType === "line" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("line")}
          >
            Line
          </Button>
          <Button
            variant={chartType === "bar" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("bar")}
          >
            Bar
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Students</p>
              <p className="text-2xl font-bold">{totalStudents}</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Level Ups</p>
              <p className="text-2xl font-bold text-green-600">{totalLevelUps}</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Completions</p>
              <p className="text-2xl font-bold text-purple-600">{totalCompletions}</p>
            </div>
            <Award className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Avg. Score</p>
              <p className="text-2xl font-bold text-orange-600">{averageScore}%</p>
            </div>
            <Target className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Progress Trend</p>
              <p className="text-2xl font-bold text-green-600">+{progressTrend}%</p>
            </div>
            <BookOpen className="h-8 w-8 text-green-600" />
          </div>
        </Card>
      </div>

      {/* Progress Trend Chart */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Monthly Progress Trends</h4>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={400}>
            {chartType === "area" ? (
              <AreaChart data={progressData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Legend />
                <Area
                  yAxisId="left"
                  type="monotone"
                  dataKey="levelUps"
                  stackId="1"
                  stroke="#00C49F"
                  fill="#00C49F"
                  fillOpacity={0.6}
                  name="Level Ups"
                />
                <Area
                  yAxisId="left"
                  type="monotone"
                  dataKey="completions"
                  stackId="1"
                  stroke="#0088FE"
                  fill="#0088FE"
                  fillOpacity={0.6}
                  name="Completions"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="averageScore"
                  stroke="#FF8042"
                  strokeWidth={3}
                  dot={{ fill: "#FF8042", strokeWidth: 2, r: 4 }}
                  name="Average Score (%)"
                />
              </AreaChart>
            ) : chartType === "line" ? (
              <LineChart data={progressData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Legend />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="levelUps"
                  stroke="#00C49F"
                  strokeWidth={3}
                  dot={{ fill: "#00C49F", strokeWidth: 2, r: 4 }}
                  name="Level Ups"
                />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="completions"
                  stroke="#0088FE"
                  strokeWidth={2}
                  dot={{ fill: "#0088FE", strokeWidth: 2, r: 3 }}
                  name="Completions"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="averageScore"
                  stroke="#FF8042"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "#FF8042", strokeWidth: 2, r: 3 }}
                  name="Average Score (%)"
                />
              </LineChart>
            ) : (
              <BarChart data={progressData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="levelUps" fill="#00C49F" name="Level Ups" />
                <Bar dataKey="completions" fill="#0088FE" name="Completions" />
                <Bar dataKey="totalAssessments" fill="#FFBB28" name="Total Assessments" />
              </BarChart>
            )}
          </ResponsiveContainer>
        )}
      </Card>

      {/* Level Distribution and Course Completion */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="text-lg font-medium mb-4">Student Level Distribution</h4>
          {loading ? (
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={levelData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ level, percentage }) => `${level} (${percentage}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="students"
                >
                  {levelData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          )}
        </Card>

        <Card className="p-6">
          <h4 className="text-lg font-medium mb-4">Course Completion Rates</h4>
          {loading ? (
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={courseData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" domain={[0, 100]} />
                <YAxis dataKey="course" type="category" width={120} />
                <Tooltip formatter={(value) => [`${value}%`, "Completion Rate"]} />
                <Bar dataKey="completionRate" fill="#8884D8" />
              </BarChart>
            </ResponsiveContainer>
          )}
        </Card>
      </div>

      {/* Level Distribution Breakdown */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Level Distribution Details</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {levelData.map((level) => (
            <div key={level.level} className="text-center">
              <div
                className="w-16 h-16 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold text-lg"
                style={{ backgroundColor: level.color }}
              >
                {level.level}
              </div>
              <div className="font-semibold">{level.students}</div>
              <div className="text-sm text-gray-500">{level.percentage}%</div>
            </div>
          ))}
        </div>
      </Card>

      {/* Course Completion Details */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Course Completion Details</h4>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">Course</th>
                <th className="text-right py-2">Completed</th>
                <th className="text-right py-2">In Progress</th>
                <th className="text-right py-2">Total</th>
                <th className="text-right py-2">Completion Rate</th>
                <th className="text-right py-2">Status</th>
              </tr>
            </thead>
            <tbody>
              {courseData.map((course) => {
                const total = course.completed + course.inProgress
                const getStatusColor = (rate: number) => {
                  if (rate >= 40) return "text-green-600"
                  if (rate >= 30) return "text-yellow-600"
                  return "text-red-600"
                }

                return (
                  <tr key={course.course} className="border-b">
                    <td className="py-2 font-medium">{course.course}</td>
                    <td className="text-right py-2 text-green-600">{course.completed}</td>
                    <td className="text-right py-2 text-blue-600">{course.inProgress}</td>
                    <td className="text-right py-2">{total}</td>
                    <td className={`text-right py-2 font-medium ${getStatusColor(course.completionRate)}`}>
                      {course.completionRate}%
                    </td>
                    <td className="text-right py-2">
                      <Badge 
                        variant={course.completionRate >= 40 ? "default" : course.completionRate >= 30 ? "secondary" : "destructive"}
                      >
                        {course.completionRate >= 40 ? "Good" : course.completionRate >= 30 ? "Average" : "Low"}
                      </Badge>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Monthly Progress Summary */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Monthly Progress Summary</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-green-50 p-4 rounded-lg">
            <h5 className="text-sm font-medium text-green-600">Overall Completion Rate</h5>
            <p className="text-2xl font-bold text-green-900">{overallCompletionRate}%</p>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h5 className="text-sm font-medium text-blue-600">Monthly Level Ups</h5>
            <p className="text-2xl font-bold text-blue-900">{currentMonth?.levelUps || 0}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h5 className="text-sm font-medium text-purple-600">Monthly Completions</h5>
            <p className="text-2xl font-bold text-purple-900">{currentMonth?.completions || 0}</p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h5 className="text-sm font-medium text-orange-600">Current Avg. Score</h5>
            <p className="text-2xl font-bold text-orange-900">{currentMonth?.averageScore || 0}%</p>
          </div>
        </div>
      </Card>
    </div>
  )
}
