'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  Card, CardContent, CardDescription, CardHeader, CardTitle,
  Button, Badge, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue,
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow,
  Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger,
  Alert, AlertDescription
} from '../../../../components/ui'
import { formatDate } from '../../../../lib/utils'
import { Search, Plus, Calendar, Users, CheckCircle, XCircle, Clock, AlertCircle, Edit, Trash2, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from 'lucide-react'
import AttendanceForm from '../../../../components/forms/attendance-form'
import { useSession } from 'next-auth/react'

interface Attendance {
  id: string
  studentId: string
  classId: string
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED'
  notes: string | null
  createdAt: string
  student: {
    user: {
      id: string
      name: string
      phone: string
    }
  }
  class: {
    id: string
    date: string
    topic: string | null
    group: {
      id: string
      name: string
    }
    teacher: {
      user: {
        name: string
      }
    }
  }
}

export default function AttendancePage() {
  const { data: session } = useSession()
  const [attendances, setAttendances] = useState<Attendance[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [dateFilter, setDateFilter] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingAttendance, setEditingAttendance] = useState<Attendance | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchAttendances = useCallback(async () => {
    try {
      setLoading(true)
      let url = '/api/attendance?limit=50'

      if (statusFilter !== 'ALL') {
        url += `&status=${statusFilter}`
      }

      if (dateFilter) {
        url += `&dateFrom=${dateFilter}&dateTo=${dateFilter}`
      }

      // For teachers, add teacher filter to only show their students
      if (session?.user?.role === 'TEACHER' && session?.user?.id) {
        url += `&teacherId=${session.user.id}`
      }

      const response = await fetch(url)
      const data = await response.json()
      setAttendances(data.attendances || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching attendances:', error)
      setError('Failed to fetch attendance records')
    } finally {
      setLoading(false)
    }
  }, [statusFilter, dateFilter, session?.user?.role, session?.user?.id])

  // Handle attendance creation
  const handleCreateAttendance = async (data: any) => {
    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create attendance')
      }

      setIsCreateDialogOpen(false)
      fetchAttendances() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle attendance update
  const handleUpdateAttendance = async (data: any) => {
    if (!editingAttendance) return

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/attendance/${editingAttendance.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update attendance')
      }

      setIsEditDialogOpen(false)
      setEditingAttendance(null)
      fetchAttendances() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle attendance deletion
  const handleDeleteAttendance = async (attendanceId: string) => {
    if (!confirm('Are you sure you want to delete this attendance record?')) {
      return
    }

    try {
      const response = await fetch(`/api/attendance/${attendanceId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete attendance')
      }

      fetchAttendances() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  useEffect(() => {
    fetchAttendances()
  }, [fetchAttendances])

  const filteredAttendances = attendances.filter(attendance =>
    attendance.student.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    attendance.student.user.phone.includes(searchTerm) ||
    attendance.class.group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    attendance.class.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PRESENT':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'ABSENT':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'LATE':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'EXCUSED':
        return <AlertCircle className="h-4 w-4 text-blue-600" />
      default:
        return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PRESENT':
        return 'bg-green-100 text-green-800'
      case 'ABSENT':
        return 'bg-red-100 text-red-800'
      case 'LATE':
        return 'bg-yellow-100 text-yellow-800'
      case 'EXCUSED':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate statistics
  const totalAttendances = attendances.length
  const presentCount = attendances.filter(a => a.status === 'PRESENT').length
  const absentCount = attendances.filter(a => a.status === 'ABSENT').length
  const lateCount = attendances.filter(a => a.status === 'LATE').length
  const excusedCount = attendances.filter(a => a.status === 'EXCUSED').length
  const attendanceRate = totalAttendances > 0 ? ((presentCount + lateCount) / totalAttendances) * 100 : 0

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading attendance records...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Attendance Tracking</h1>
          <p className="text-gray-600">Monitor and manage student attendance</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Mark Attendance
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-5xl">
            <DialogHeader>
              <DialogTitle>Mark Class Attendance</DialogTitle>
              <DialogDescription>
                Select a class and mark attendance for all students.
              </DialogDescription>
            </DialogHeader>
            <AttendanceForm
              onSubmit={handleCreateAttendance}
              onCancel={() => setIsCreateDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by student, group, or teacher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="PRESENT">Present</SelectItem>
                <SelectItem value="ABSENT">Absent</SelectItem>
                <SelectItem value="LATE">Late</SelectItem>
                <SelectItem value="EXCUSED">Excused</SelectItem>
              </SelectContent>
            </Select>
            <Input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              placeholder="Filter by date"
            />
          </div>
        </CardContent>
      </Card>

      {/* Attendance Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Records</p>
                <p className="text-2xl font-bold text-gray-900">{totalAttendances}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Present</p>
                <p className="text-2xl font-bold text-gray-900">{presentCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Absent</p>
                <p className="text-2xl font-bold text-gray-900">{absentCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Late</p>
                <p className="text-2xl font-bold text-gray-900">{lateCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertCircle className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Attendance Rate</p>
                <p className="text-2xl font-bold text-gray-900">{attendanceRate.toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Attendance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Attendance Records ({filteredAttendances.length})</CardTitle>
          <CardDescription>
            Recent attendance records across all classes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Teacher</TableHead>
                <TableHead>Class Date</TableHead>
                <TableHead>Topic</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAttendances.map((attendance) => (
                <TableRow key={attendance.id}>
                  <TableCell>
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {attendance.student.user.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {attendance.student.user.phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">{attendance.class.group.name}</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{attendance.class.teacher.user.name}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      <span className="text-sm">{formatDate(attendance.class.date)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">
                      {attendance.class.topic || 'No topic'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(attendance.status)}>
                      <div className="flex items-center">
                        {getStatusIcon(attendance.status)}
                        <span className="ml-1">{attendance.status}</span>
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">
                      {attendance.notes || '-'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingAttendance(attendance)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteAttendance(attendance.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Attendance Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-5xl">
          <DialogHeader>
            <DialogTitle>Edit Attendance</DialogTitle>
            <DialogDescription>
              Update attendance record for this student.
            </DialogDescription>
          </DialogHeader>
          {editingAttendance && (
            <AttendanceForm
              preselectedClassId={editingAttendance.classId}
              initialData={{
                classId: editingAttendance.classId,
                attendances: [{
                  studentId: editingAttendance.studentId,
                  status: editingAttendance.status as any,
                  notes: editingAttendance.notes || ''
                }]
              }}
              onSubmit={handleUpdateAttendance}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingAttendance(null)
              }}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
