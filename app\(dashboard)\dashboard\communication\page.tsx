'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card'
import { Button } from '../../../../components/ui/button'
import { Input } from '../../../../components/ui/input'
import { Badge } from '../../../../components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../../components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs'
import { Alert, AlertDescription } from '../../../../components/ui/alert'
import { Textarea } from '../../../../components/ui/textarea'
import { MessageSquare, Phone, Mail, Send, Users, Bell, Settings, CheckCircle, XCircle, Zap } from 'lucide-react'

export default function CommunicationPage() {
  const [selectedTab, setSelectedTab] = useState('sms')
  const [message, setMessage] = useState('')
  const [recipient, setRecipient] = useState('')

  // New state for enhanced features
  const [workflows, setWorkflows] = useState<any[]>([])
  const [serviceStatus, setServiceStatus] = useState({ sms: false, email: false })
  const [loading, setLoading] = useState(false)
  const [alertMessage, setAlertMessage] = useState('')
  const [notificationType, setNotificationType] = useState('reminder')
  const [priority, setPriority] = useState('medium')
  const [channels, setChannels] = useState<string[]>(['email'])

  const [communicationStats, setCommunicationStats] = useState([
    { label: 'SMS Sent Today', value: '0', icon: MessageSquare, color: 'text-blue-600' },
    { label: 'Calls Made', value: '0', icon: Phone, color: 'text-green-600' },
    { label: 'Emails Sent', value: '0', icon: Mail, color: 'text-purple-600' },
    { label: 'Active Notifications', value: '0', icon: Bell, color: 'text-orange-600' },
  ])

  const recentMessages = [
    {
      id: 1,
      type: 'SMS',
      recipient: 'Aziza Karimova',
      message: 'Reminder: Your IELTS class starts tomorrow at 10:00 AM',
      status: 'Delivered',
      timestamp: '2 hours ago',
    },
    {
      id: 2,
      type: 'WhatsApp',
      recipient: 'Bobur Toshev',
      message: 'Your payment has been received. Thank you!',
      status: 'Read',
      timestamp: '4 hours ago',
    },
    {
      id: 3,
      type: 'SMS',
      recipient: 'Group A1-Morning',
      message: 'Class moved to Room 205 today',
      status: 'Delivered',
      timestamp: '6 hours ago',
    },
  ]

  const templates = [
    { id: 1, name: 'Class Reminder', content: 'Reminder: Your {course} class starts {time} at {location}' },
    { id: 2, name: 'Payment Confirmation', content: 'Your payment of {amount} has been received. Thank you!' },
    { id: 3, name: 'Welcome Message', content: 'Welcome to Innovative Centre! Your journey to English mastery begins now.' },
    { id: 4, name: 'Assignment Reminder', content: 'Don\'t forget to complete your homework for tomorrow\'s class.' },
  ]

  // Fetch workflows and service status
  useEffect(() => {
    fetchWorkflows()
    fetchServiceStatus()
    fetchCommunicationStats()
  }, [])

  const fetchCommunicationStats = async () => {
    try {
      const response = await fetch('/api/communication/stats?period=today')
      if (response.ok) {
        const data = await response.json()
        setCommunicationStats([
          { label: 'SMS Sent Today', value: data.stats.sms.total.toString(), icon: MessageSquare, color: 'text-blue-600' },
          { label: 'Calls Made', value: data.stats.calls.total.toString(), icon: Phone, color: 'text-green-600' },
          { label: 'Emails Sent', value: data.stats.email.total.toString(), icon: Mail, color: 'text-purple-600' },
          { label: 'Active Notifications', value: data.stats.notifications.active.toString(), icon: Bell, color: 'text-orange-600' },
        ])
      }
    } catch (error) {
      console.error('Error fetching communication stats:', error)
    }
  }

  const fetchWorkflows = async () => {
    try {
      const response = await fetch('/api/workflows?action=list')
      if (response.ok) {
        const data = await response.json()
        setWorkflows(data.workflows)
      }
    } catch (error) {
      console.error('Error fetching workflows:', error)
    }
  }

  const fetchServiceStatus = async () => {
    try {
      const response = await fetch('/api/notifications?action=test')
      if (response.ok) {
        const data = await response.json()
        setServiceStatus({
          sms: data.sms.available,
          email: data.email.available,
        })
      }
    } catch (error) {
      console.error('Error fetching service status:', error)
    }
  }

  const sendNotification = async () => {
    if (!recipient || !message) {
      setAlertMessage('Please select a recipient and enter a message')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          recipientIds: [recipient],
          recipientType: 'student',
          type: notificationType,
          priority,
          channels,
          data: { message, studentName: 'Student' },
        }),
      })

      const data = await response.json()
      setAlertMessage(data.message)
      if (data.success) {
        setMessage('')
        setRecipient('')
      }
    } catch (error) {
      setAlertMessage('Failed to send notification')
    } finally {
      setLoading(false)
    }
  }

  const toggleWorkflow = async (workflowId: string, enabled: boolean) => {
    try {
      const response = await fetch('/api/workflows', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ workflowId, enabled }),
      })

      if (response.ok) {
        fetchWorkflows()
        setAlertMessage(`Workflow ${enabled ? 'enabled' : 'disabled'} successfully`)
      }
    } catch (error) {
      setAlertMessage('Failed to update workflow')
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Communication Center</h1>
          <p className="text-gray-600">Send messages, manage workflows, and configure notifications</p>
        </div>
      </div>

      {alertMessage && (
        <Alert>
          <AlertDescription>{alertMessage}</AlertDescription>
        </Alert>
      )}

      {/* Communication Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {communicationStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <stat.icon className={`h-8 w-8 ${stat.color}`} />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="send" className="space-y-6">
        <TabsList>
          <TabsTrigger value="send">Send Messages</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="send">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Message Composer */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Send Message</CardTitle>
              <CardDescription>Compose and send messages to students or groups</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Message Type Tabs */}
              <div className="flex space-x-2">
                {['sms', 'whatsapp', 'email', 'notification'].map((type) => (
                  <Button
                    key={type}
                    variant={selectedTab === type ? 'default' : 'outline'}
                    onClick={() => setSelectedTab(type)}
                    size="sm"
                  >
                    {type.toUpperCase()}
                  </Button>
                ))}
              </div>

              {/* Recipient Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Recipient</label>
                <Select value={recipient} onValueChange={setRecipient}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select recipient" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-students">All Students</SelectItem>
                    <SelectItem value="group-a1">Group A1 - Morning</SelectItem>
                    <SelectItem value="group-b1">Group B1 - Evening</SelectItem>
                    <SelectItem value="ielts-students">IELTS Students</SelectItem>
                    <SelectItem value="individual">Individual Student</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Message Content */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Message</label>
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-md resize-none"
                  rows={4}
                  placeholder="Type your message here..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                />
                <div className="text-sm text-gray-500">
                  {message.length}/160 characters
                </div>
              </div>

              {/* Send Options */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="schedule" className="rounded" />
                  <label htmlFor="schedule" className="text-sm">Schedule for later</label>
                </div>
                <Button className="flex items-center space-x-2">
                  <Send className="h-4 w-4" />
                  <span>Send Message</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Messages */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Recent Messages</CardTitle>
              <CardDescription>Latest sent messages and their status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentMessages.map((msg) => (
                  <div key={msg.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <MessageSquare className="h-4 w-4 text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900">{msg.recipient}</p>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{msg.type}</Badge>
                          <Badge 
                            className={
                              msg.status === 'Delivered' ? 'bg-green-100 text-green-800' :
                              msg.status === 'Read' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }
                          >
                            {msg.status}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{msg.message}</p>
                      <p className="text-xs text-gray-400 mt-1">{msg.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Templates and Quick Actions */}
        <div className="space-y-6">
          {/* Message Templates */}
          <Card>
            <CardHeader>
              <CardTitle>Message Templates</CardTitle>
              <CardDescription>Quick templates for common messages</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {templates.map((template) => (
                  <div key={template.id} className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                    <h4 className="font-medium text-sm">{template.name}</h4>
                    <p className="text-xs text-gray-600 mt-1">{template.content}</p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-2"
                      onClick={() => setMessage(template.content)}
                    >
                      Use Template
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common communication tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Send Class Reminder
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Bell className="h-4 w-4 mr-2" />
                  Payment Reminder
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Welcome New Students
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Phone className="h-4 w-4 mr-2" />
                  Schedule Follow-up
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Communication Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
              <CardDescription>Communication preferences</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">SMS Notifications</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Email Notifications</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">WhatsApp Integration</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto Reminders</span>
                  <input type="checkbox" defaultChecked className="rounded" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
          </div>
        </TabsContent>

        {/* Workflows Tab */}
        <TabsContent value="workflows">
          <Card>
            <CardHeader>
              <CardTitle>Automated Workflows</CardTitle>
              <CardDescription>Manage automated business processes and notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {workflows.map((workflow) => (
                  <div key={workflow.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <Zap className="h-5 w-5 text-blue-600" />
                        <div>
                          <h3 className="font-medium">{workflow.name}</h3>
                          <p className="text-sm text-gray-500">{workflow.description}</p>
                          <div className="mt-1">
                            <Badge variant="outline" size="sm">
                              {workflow.trigger.event}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={workflow.enabled ? "default" : "secondary"}>
                        {workflow.enabled ? "Enabled" : "Disabled"}
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleWorkflow(workflow.id, !workflow.enabled)}
                      >
                        {workflow.enabled ? "Disable" : "Enable"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Communication Settings</CardTitle>
              <CardDescription>Configure SMS and email service providers</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Service Status</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Phone className="h-5 w-5 text-blue-600" />
                        <span>SMS Service</span>
                      </div>
                      {serviceStatus.sms ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Mail className="h-5 w-5 text-green-600" />
                        <span>Email Service</span>
                      </div>
                      {serviceStatus.email ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Notification Preferences</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">SMS Notifications</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Email Notifications</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Auto Reminders</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Payment Alerts</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                  </div>
                </div>

                <Button>
                  <Settings className="h-4 w-4 mr-2" />
                  Save Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
