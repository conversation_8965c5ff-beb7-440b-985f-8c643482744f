# Authentication System Documentation

## 🏗️ System Architecture Overview

The Innovative Centre CRM uses a **dual-server architecture** with sophisticated authentication mechanisms:

```
┌─────────────────────┐    Inter-Server    ┌─────────────────────┐
│    Admin Server     │ ←──────────────────→ │    Staff Server     │
│                     │    Communication    │                     │
│ • Full Database     │                     │ • Limited Access    │
│ • All User Roles    │                     │ • Staff Roles Only  │
│ • Financial Data    │                     │ • Daily Operations  │
│ • ADMIN/CASHIER     │                     │ • TEACHER/RECEPTION │
└─────────────────────┘                     └─────────────────────┘
```

### Server Types
- **Admin Server**: `inno-crm-admin.vercel.app` - Full access, financial data, admin operations
- **Staff Server**: `inno-crm-staff.vercel.app` - Limited access, daily operations, staff functions

## 🔐 Authentication Flow

### 1. Admin Server Authentication
**Direct Database Authentication**

```typescript
// lib/auth.ts (Admin Server)
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      async authorize(credentials) {
        // 1. Validate credentials
        if (!credentials?.phone || !credentials?.password) return null;
        
        // 2. Query local database
        const user = await prisma.user.findUnique({
          where: { phone: credentials.phone }
        });
        
        // 3. Verify password with bcrypt
        const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
        
        // 4. Check role permissions for admin server
        const allowedRoles = ['ADMIN', 'CASHIER', 'MANAGER', 'TEACHER', 'RECEPTION', 'STUDENT', 'ACADEMIC_MANAGER'];
        
        if (isPasswordValid && allowedRoles.includes(user.role)) {
          return userWithoutPassword;
        }
        
        return null;
      }
    })
  ]
}
```

### 2. Staff Server Authentication
**Inter-Server Authentication via Admin Server**

```typescript
// lib/auth.ts (Staff Server)
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      async authorize(credentials) {
        // 1. Validate credentials
        if (!credentials?.phone || !credentials?.password) return null;
        
        // 2. Authenticate via Admin Server API
        const authResult = await StaffServerAPI.authenticateUser(
          credentials.phone,
          credentials.password
        );
        
        // 3. Verify role is allowed on staff server
        const allowedRoles = ['RECEPTION', 'ACADEMIC_MANAGER', 'TEACHER', 'MANAGER'];
        
        if (authResult.success && allowedRoles.includes(authResult.data.user.role)) {
          return authResult.data.user;
        }
        
        return null;
      }
    })
  ]
}
```

## 🔑 User Roles and Permissions

### Role Hierarchy
```typescript
enum Role {
  ADMIN           // Full system access (Admin Server only)
  MANAGER         // Management operations (Both servers)
  TEACHER         // Teaching and student management (Both servers)
  RECEPTION       // Lead and enrollment management (Both servers)
  CASHIER         // Payment processing (Admin Server only)
  STUDENT         // Student portal access (Both servers)
  ACADEMIC_MANAGER // Academic oversight (Both servers)
}
```

### Server-Specific Role Access

#### Admin Server Roles
- ✅ **ADMIN**: Full access to all features, financial reports, analytics
- ✅ **CASHIER**: Payment processing, financial transactions
- ✅ **MANAGER**: Management operations, reports
- ✅ **TEACHER**: Teaching, student management
- ✅ **RECEPTION**: Lead management, enrollments
- ✅ **STUDENT**: Student portal
- ✅ **ACADEMIC_MANAGER**: Academic oversight

#### Staff Server Roles
- ❌ **ADMIN**: Not allowed (admin operations restricted)
- ❌ **CASHIER**: Not allowed (financial operations restricted)
- ✅ **MANAGER**: Management operations
- ✅ **TEACHER**: Teaching, student management
- ✅ **RECEPTION**: Lead management, enrollments
- ✅ **STUDENT**: Student portal
- ✅ **ACADEMIC_MANAGER**: Academic oversight

## 🛡️ Security Implementation

### 1. Password Security
```typescript
// Password hashing with bcrypt
const hashedPassword = await bcrypt.hash(password, 12);

// Password verification
const isValid = await bcrypt.compare(plainPassword, hashedPassword);
```

### 2. JWT Session Management
```typescript
// NextAuth JWT configuration
session: {
  strategy: "jwt"
},
callbacks: {
  async jwt({ token, user }) {
    if (user) {
      token.role = user.role || null;
    }
    return token;
  },
  async session({ session, token }) {
    if (token) {
      session.user.id = token.sub!;
      session.user.role = (token.role as string) || null;
    }
    return session;
  }
}
```

### 3. Inter-Server Authentication
```typescript
// Secure communication between servers
const INTER_SERVER_SECRET = process.env.INTER_SERVER_SECRET;

// Request validation
function validateInterServerAuth(request: NextRequest): boolean {
  const secret = request.headers.get('X-Inter-Server-Secret');
  const userAgent = request.headers.get('User-Agent');
  
  return secret === INTER_SERVER_SECRET && 
         (userAgent === 'admin-server' || userAgent === 'staff-server');
}
```

## 🔄 Inter-Server Communication

### Authentication Endpoint
```typescript
// Admin Server: /api/inter-server/auth/validate
export async function POST(request: NextRequest) {
  // 1. Validate inter-server authentication
  if (!validateInterServerAuth(request)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // 2. Extract credentials
  const { phone, password } = await request.json();
  
  // 3. Find user in admin database
  const user = await prisma.user.findUnique({
    where: { phone }
  });
  
  // 4. Verify password
  const isPasswordValid = await bcrypt.compare(password, user.password);
  
  // 5. Check staff server role permissions
  const allowedRoles = ['MANAGER', 'TEACHER', 'RECEPTION', 'STUDENT', 'ACADEMIC_MANAGER'];
  
  if (isPasswordValid && allowedRoles.includes(user.role)) {
    return NextResponse.json({
      success: true,
      user: userWithoutPassword
    });
  }
  
  return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
}
```

### Staff Server API Client
```typescript
// lib/inter-server.ts
export class StaffServerAPI {
  static async authenticateUser(phone: string, password: string): Promise<InterServerResponse> {
    return makeInterServerRequest('admin', {
      endpoint: '/api/inter-server/auth/validate',
      method: 'POST',
      data: { phone, password }
    });
  }
}
```

## 🛣️ Route Protection

### Middleware Implementation
```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 1. Skip static files
  if (pathname.startsWith('/_next') || pathname.includes('.')) {
    return NextResponse.next();
  }
  
  // 2. Allow public routes
  const publicRoutes = ['/auth/signin', '/api/auth', '/api/health'];
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }
  
  // 3. Get JWT token
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  });
  
  // 4. Redirect to signin if no token
  if (!token) {
    const signInUrl = new URL('/auth/signin', request.url);
    signInUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(signInUrl);
  }
  
  // 5. Check role-based access
  const userRole = token.role as string;
  const serverType = process.env.SERVER_TYPE || 'staff';
  
  // Staff server: Only allow staff roles
  if (serverType === 'staff') {
    const allowedRoles = ['RECEPTION', 'ACADEMIC_MANAGER', 'TEACHER', 'MANAGER'];
    if (!allowedRoles.includes(userRole)) {
      return NextResponse.redirect(new URL('/auth/signin?error=unauthorized', request.url));
    }
  }
  
  return NextResponse.next();
}
```

### API Route Protection
```typescript
// Protected API routes with role-based access
const protectedApiRoutes = {
  // Admin Server
  '/api/analytics': ['ADMIN'],
  '/api/reports': ['ADMIN'],
  '/api/payments': ['ADMIN', 'CASHIER'],
  
  // Staff Server
  '/api/teachers': ['MANAGER'],
  '/api/students': ['MANAGER', 'TEACHER', 'RECEPTION', 'ACADEMIC_MANAGER'],
  '/api/groups': ['MANAGER', 'TEACHER'],
  '/api/enrollments': ['MANAGER', 'RECEPTION'],
  '/api/attendance': ['MANAGER', 'TEACHER'],
  '/api/assessments': ['MANAGER', 'TEACHER', 'ACADEMIC_MANAGER'],
  '/api/leads': ['MANAGER', 'RECEPTION'],
  '/api/courses': ['MANAGER']
};
```

## 📱 User Interface Flow

### Login Process
1. **User Access**: User visits either admin or staff server
2. **Login Form**: Enters phone number and password
3. **Credential Validation**: Server validates credentials
4. **Role-Based Redirect**: User redirected based on role

```typescript
// Role-based dashboard redirection
switch (userRole) {
  case "STUDENT":
    router.push("/dashboard/student");
    break;
  case "TEACHER":
    router.push("/dashboard/teacher");
    break;
  case "RECEPTION":
    router.push("/dashboard/leads");
    break;
  case "CASHIER":
    router.push("/dashboard/payments");
    break;
  case "ACADEMIC_MANAGER":
    router.push("/dashboard/assessments");
    break;
  default:
    router.push("/dashboard");
}
```

## 🔧 Environment Configuration

### Required Environment Variables
```env
# Authentication
NEXTAUTH_SECRET="unique-secret-for-each-server"
NEXTAUTH_URL="https://your-server-domain.vercel.app"

# Server Configuration
SERVER_TYPE="admin" # or "staff"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="shared-secret-between-servers"

# Database
DATABASE_URL="postgresql://connection-string"
```

## 🧪 Testing Authentication

### Manual Testing
```bash
# Test admin server login
curl -X POST https://inno-crm-admin.vercel.app/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"phone":"+998901234567","password":"password123"}'

# Test staff server login
curl -X POST https://inno-crm-staff.vercel.app/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"phone":"+998901234567","password":"password123"}'

# Test inter-server authentication
curl -X POST https://inno-crm-admin.vercel.app/api/inter-server/auth/validate \
  -H "Content-Type: application/json" \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: staff-server" \
  -d '{"phone":"+998901234567","password":"password123"}'
```

## 🚨 Security Considerations

### Best Practices
1. **Password Security**: Minimum 8 characters, bcrypt hashing with 12 rounds
2. **Session Management**: JWT tokens with 7-day expiration
3. **Role Validation**: Server-side role checking on every request
4. **Inter-Server Security**: Shared secret authentication
5. **HTTPS Only**: All communications over secure connections
6. **Input Validation**: Zod schema validation for all inputs
7. **Rate Limiting**: Protection against brute force attacks

### Common Security Issues
- ❌ **Weak Passwords**: Enforce strong password policies
- ❌ **Session Hijacking**: Use secure JWT tokens
- ❌ **Role Escalation**: Validate roles on server-side
- ❌ **Inter-Server Attacks**: Secure shared secrets
- ❌ **CSRF Attacks**: NextAuth CSRF protection enabled

## 📊 Monitoring and Logging

### Authentication Events
- User login attempts (success/failure)
- Role-based access denials
- Inter-server authentication requests
- Session creation and expiration
- Password change attempts

### Health Checks
```typescript
// Health check endpoints
GET /api/health - Server health status
GET /api/inter-server/health - Inter-server connectivity
```

This authentication system provides robust security while maintaining flexibility for the dual-server architecture, ensuring proper role-based access control and secure inter-server communication.
