import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '../../../../../lib/auth'
import { prisma } from '../../../../../lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if user has permission to view this student's assignments
    if (session.user.role === 'STUDENT' && session.user.id !== id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Academic managers can view all student assignments for test statistics
    if (session.user.role === 'ACADEMIC_MANAGER') {
      // Allow access to view assignments for test statistics
    }

    // Get student information with current group and classes
    const student = await prisma.student.findUnique({
      where: { userId: id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        currentGroup: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            classes: {
              where: {
                homework: { not: null },
              },
              orderBy: { date: 'desc' },
              take: 20,
            },
          },
        },
        assessments: {
          where: {
            type: { in: ['PROGRESS_TEST', 'FINAL_EXAM'] },
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    const pending = []
    const completed = []
    const upcoming = []

    // Process homework assignments from classes
    if (student.currentGroup?.classes) {
      for (const classItem of student.currentGroup.classes) {
        if (classItem.homework) {
          const dueDate = new Date(classItem.date)
          dueDate.setDate(dueDate.getDate() + 3) // Homework due 3 days after class
          
          const isOverdue = dueDate < new Date()
          // Check if assignment is completed based on actual data
          // For now, we'll consider assignments completed if they're older than 7 days
          // In a real system, this would be tracked in a separate assignments table
          const isCompleted = dueDate < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)

          const assignment = {
            id: `hw-${classItem.id}`,
            title: `Homework: ${classItem.topic || 'Class Assignment'}`,
            course: student.currentGroup.course.name,
            type: 'homework',
            dueDate: dueDate.toISOString(),
            description: classItem.homework,
            status: isCompleted ? 'completed' : isOverdue ? 'overdue' : 'pending',
            submittedAt: isCompleted ? new Date(dueDate.getTime() - 24 * 60 * 60 * 1000).toISOString() : null,
            grade: isCompleted ? 85 : null, // Default grade for completed assignments
            feedback: isCompleted ? 'Assignment completed successfully.' : null,
          }

          if (isCompleted) {
            completed.push(assignment)
          } else if (isOverdue) {
            pending.push({ ...assignment, status: 'overdue' })
          } else {
            pending.push(assignment)
          }
        }
      }
    }

    // Process assessment assignments
    for (const assessment of student.assessments) {
      if (assessment.completedAt) {
        completed.push({
          id: assessment.id,
          title: assessment.testName,
          course: student.currentGroup?.course.name || 'Assessment',
          type: 'assessment',
          dueDate: assessment.completedAt,
          description: `${assessment.type.replace('_', ' ').toLowerCase()} assessment`,
          status: 'completed',
          submittedAt: assessment.completedAt,
          grade: assessment.score,
          maxGrade: assessment.maxScore,
          passed: assessment.passed,
        })
      }
    }

    // Generate upcoming assignments based on future classes
    if (student.currentGroup) {
      // Get future classes for the group
      const futureClasses = await prisma.class.findMany({
        where: {
          groupId: student.currentGroup.id,
          date: { gt: new Date() }
        },
        orderBy: { date: 'asc' },
        take: 3
      })

      for (const futureClass of futureClasses) {
        const dueDate = new Date(futureClass.date)
        dueDate.setDate(dueDate.getDate() + 3) // Due 3 days after class

        upcoming.push({
          id: `upcoming-${futureClass.id}`,
          title: `Assignment: ${futureClass.topic || 'Class Assignment'}`,
          course: student.currentGroup.course.name,
          type: 'homework',
          dueDate: dueDate.toISOString(),
          description: 'Assignment details will be provided in class',
          status: 'upcoming',
        })
      }
    }

    // Sort arrays
    pending.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
    completed.sort((a, b) => new Date(b.submittedAt || b.dueDate).getTime() - new Date(a.submittedAt || a.dueDate).getTime())
    upcoming.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())

    const assignmentData = {
      student: {
        name: student.user.name,
        email: student.user.email,
        level: student.level,
        branch: student.branch,
      },
      currentGroup: student.currentGroup ? {
        name: student.currentGroup.name,
        course: student.currentGroup.course.name,
        level: student.currentGroup.course.level,
      } : null,
      pending,
      completed,
      upcoming,
      statistics: {
        totalAssignments: pending.length + completed.length,
        completedAssignments: completed.length,
        pendingAssignments: pending.filter(a => a.status === 'pending').length,
        overdueAssignments: pending.filter(a => a.status === 'overdue').length,
        averageGrade: completed.filter(a => a.grade).length > 0 
          ? completed.filter(a => a.grade).reduce((sum, a) => sum + (a.grade || 0), 0) / completed.filter(a => a.grade).length
          : 0,
        completionRate: (pending.length + completed.length) > 0 
          ? (completed.length / (pending.length + completed.length)) * 100
          : 0,
      },
    }

    return NextResponse.json(assignmentData)
  } catch (error) {
    console.error('Error fetching student assignments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
