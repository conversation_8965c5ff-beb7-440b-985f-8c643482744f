const fs = require('fs');

// Function to fix malformed paths like '../../../../../@/' back to '@/'
function fixMalformedPaths(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Fix malformed paths
    let fixedContent = content
      .replace(/\.\.\/+@\//g, '@/')
      .replace(/\.\.\/+\.\.\/+@\//g, '@/');
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed malformed paths in: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No malformed paths found: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Files to check
const filesToCheck = [
  'app/(dashboard)/dashboard/page.tsx',
  'app/(dashboard)/dashboard/cabinets/page.tsx',
  'app/(dashboard)/dashboard/courses/page.tsx',
  'app/(dashboard)/dashboard/students/page.tsx',
  'app/(dashboard)/dashboard/teachers/page.tsx',
  'app/(dashboard)/dashboard/users/page.tsx',
  'app/(dashboard)/dashboard/leads/page.tsx',
  'app/(dashboard)/dashboard/groups/page.tsx',
  'app/(dashboard)/dashboard/communication/page.tsx',
  'app/(dashboard)/dashboard/unauthorized/page.tsx',
  'app/(dashboard)/dashboard/enrollments/page.tsx',
  'app/(dashboard)/dashboard/settings/page.tsx',
  'app/(dashboard)/dashboard/students/[id]/page.tsx',
  'app/(dashboard)/dashboard/groups/[id]/page.tsx',
  'app/(dashboard)/dashboard/teachers/[id]/page.tsx',
  'app/(dashboard)/dashboard/cabinets/[id]/page.tsx',
  'components/dashboard/sidebar.tsx',
  'components/dashboard/header.tsx',
  'components/dashboard/activity-feed.tsx'
];

console.log('🔧 Fixing malformed paths...\n');

let fixedCount = 0;
filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    if (fixMalformedPaths(file)) {
      fixedCount++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

console.log(`\n🎯 Summary: Fixed malformed paths in ${fixedCount} files`);
