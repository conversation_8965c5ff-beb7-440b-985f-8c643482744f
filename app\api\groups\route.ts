import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '../../../lib/prisma'
import * as z from 'zod'

const groupSchema = z.object({
  name: z.string().min(1),
  courseId: z.string(),
  teacherId: z.string(),
  capacity: z.number().min(1).max(50),
  schedule: z.string(),
  room: z.string().optional(),
  cabinetId: z.string().optional(),
  branch: z.string(),
  startDate: z.string(),
  endDate: z.string(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const branch = searchParams.get('branch')
    const isActive = searchParams.get('isActive')

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { course: { name: { contains: search, mode: 'insensitive' } } },
        { teacher: { user: { name: { contains: search, mode: 'insensitive' } } } },
      ]
    }

    if (branch) {
      // Map branch ID to branch name for database query
      const branchName = branch === 'main' ? 'Main Branch' : 'Branch'
      where.branch = branchName
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    const [groups, total] = await Promise.all([
      prisma.group.findMany({
        where,
        include: {
          course: {
            select: {
              name: true,
              level: true,
            },
          },
          teacher: {
            select: {
              id: true,
              tier: true,
              subject: true,
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          enrollments: {
            where: {
              status: 'ACTIVE'
            },
            include: {
              student: {
                include: {
                  user: {
                    select: {
                      id: true,
                      name: true,
                      phone: true,
                    },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              enrollments: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.group.count({ where }),
    ])

    // Calculate granular tier utilization per course level, schedule, and time slot
    const tierPriority: Record<string, number> = { 'A_LEVEL': 1, 'B_LEVEL': 2, 'C_LEVEL': 3, 'NEW': 4 }

    // Helper function to extract time and days from schedule
    const parseSchedule = (schedule: string) => {
      try {
        // First try to parse as JSON (for backward compatibility)
        let scheduleStr = schedule
        try {
          const parsed = JSON.parse(schedule)
          scheduleStr = Array.isArray(parsed) ? parsed.join(' ') : schedule
        } catch {
          // If JSON parsing fails, use the string as-is
          scheduleStr = schedule
        }

        // Extract time (e.g., "9:00-11:00" or "14:00-16:00")
        const timeMatch = scheduleStr.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
        const time = timeMatch ? `${timeMatch[1]}:${timeMatch[2]}-${timeMatch[3]}:${timeMatch[4]}` : 'Unknown'

        // Extract days (M/W/F or T/T/S)
        const lowerSchedule = scheduleStr.toLowerCase()
        const days = lowerSchedule.includes('monday') &&
                    lowerSchedule.includes('wednesday') &&
                    lowerSchedule.includes('friday') ? 'MWF' :
                    lowerSchedule.includes('tuesday') &&
                    lowerSchedule.includes('thursday') &&
                    lowerSchedule.includes('saturday') ? 'TTS' : 'Other'

        return { time, days }
      } catch (error) {
        console.error('Error parsing schedule:', error, 'Schedule:', schedule)
        return { time: 'Unknown', days: 'Unknown' }
      }
    }

    // Group by course level, schedule pattern, and time slot
    const slotGroups = groups.reduce((acc, group) => {
      const { time, days } = parseSchedule(group.schedule)
      const slotKey = `${group.course.level}-${days}-${time}`

      if (!acc[slotKey]) {
        acc[slotKey] = {
          courseLevel: group.course.level,
          days,
          time,
          groups: []
        }
      }
      acc[slotKey].groups.push(group)
      return acc
    }, {} as Record<string, any>)

    // Calculate tier progression for each slot
    const slotTierAnalysis = Object.entries(slotGroups).map(([slotKey, slotData]) => {
      const tierGroups = slotData.groups.reduce((acc: any, group: any) => {
        const tier = group.teacher.tier || 'NEW'
        if (!acc[tier]) acc[tier] = []
        acc[tier].push(group)
        return acc
      }, {})

      const tierUtilization = Object.entries(tierGroups).map(([tier, tierGroupList]: [string, any]) => {
        const totalCapacity = tierGroupList.reduce((sum: number, group: any) => sum + group.capacity, 0)
        const totalEnrolled = tierGroupList.reduce((sum: number, group: any) => sum + group._count.enrollments, 0)
        const utilizationRate = totalCapacity > 0 ? (totalEnrolled / totalCapacity) * 100 : 0

        return {
          tier,
          groupCount: tierGroupList.length,
          utilizationRate: Math.round(utilizationRate * 100) / 100,
          priority: tierPriority[tier] || 4,
          groups: tierGroupList
        }
      }).sort((a, b) => a.priority - b.priority)

      // Determine which tiers are available for this slot
      const availableTiers = []
      for (const tierData of tierUtilization) {
        if (tierData.tier === 'A_LEVEL') {
          // A-Level always available
          availableTiers.push(tierData.tier)
        } else {
          // Check if higher tiers are 80%+ utilized
          const higherTiers = tierUtilization.filter(t => t.priority < tierData.priority)
          const allHigherTiersUtilized = higherTiers.every(t => t.utilizationRate >= 80)

          if (allHigherTiersUtilized || higherTiers.length === 0) {
            availableTiers.push(tierData.tier)
          }
        }
      }

      return {
        slotKey,
        courseLevel: slotData.courseLevel,
        days: slotData.days,
        time: slotData.time,
        tierUtilization,
        availableTiers,
        totalGroups: slotData.groups.length
      }
    })

    return NextResponse.json({
      groups,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      slotTierAnalysis,
    })
  } catch (error) {
    console.error('Error fetching groups:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = groupSchema.parse(body)

    const group = await prisma.group.create({
      data: {
        ...validatedData,
        startDate: new Date(validatedData.startDate),
        endDate: new Date(validatedData.endDate),
      },
      include: {
        course: {
          select: {
            name: true,
            level: true,
          },
        },
        teacher: {
          select: {
            id: true,
            tier: true,
            subject: true,
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            enrollments: true,
          },
        },
      },
    })

    return NextResponse.json(group, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating group:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
