// Test Vercel debug endpoints
async function testVercelDebug() {
  const baseUrl = 'https://inno-crm-admin.vercel.app'
  
  console.log('🔍 Testing Vercel Debug Endpoints...\n')
  
  try {
    // Test 1: Environment check
    console.log('1️⃣ Checking environment variables and database...')
    const envResponse = await fetch(`${baseUrl}/api/debug/env`)
    
    if (envResponse.ok) {
      const envData = await envResponse.json()
      console.log('✅ Environment Check Results:')
      console.log(JSON.stringify(envData, null, 2))
    } else {
      console.log(`❌ Environment check failed: ${envResponse.status}`)
    }
    
    console.log('\n' + '='.repeat(50) + '\n')
    
    // Test 2: Authentication test
    console.log('2️⃣ Testing authentication with working credentials...')
    const authResponse = await fetch(`${baseUrl}/api/debug/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: '+998906006299',
        password: 'Parviz0106$'
      })
    })
    
    if (authResponse.ok) {
      const authData = await authResponse.json()
      console.log('✅ Authentication Test Results:')
      console.log(JSON.stringify(authData, null, 2))
    } else {
      console.log(`❌ Authentication test failed: ${authResponse.status}`)
      const errorText = await authResponse.text()
      console.log('Error:', errorText)
    }
    
  } catch (error) {
    console.error('❌ Error testing Vercel:', error.message)
  }
}

// Run the test
testVercelDebug()
