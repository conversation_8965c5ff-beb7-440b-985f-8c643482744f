"use client"

import { useState, useEffect, useCallback } from "react"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import { Card } from "../../components/ui/card"
import { Button } from "../../components/ui/button"
import { Select } from "../../components/ui/select"
import { Calendar, TrendingUp, DollarSign, BarChart3 } from "lucide-react"

interface RevenueData {
  month: string
  revenue: number
  payments: number
  target: number
}

interface PaymentMethodData {
  method: string
  amount: number
  percentage: number
}

interface RevenueChartProps {
  className?: string
}

export function RevenueChart({ className }: RevenueChartProps) {
  const [revenueData, setRevenueData] = useState<RevenueData[]>([])
  const [paymentMethodData, setPaymentMethodData] = useState<PaymentMethodData[]>([])
  const [chartType, setChartType] = useState<"line" | "bar">("line")
  const [timeRange, setTimeRange] = useState("12months")
  const [loading, setLoading] = useState(true)

  // Fetch revenue analytics data
  const fetchRevenueData = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/analytics/revenue?range=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setRevenueData(data.monthlyRevenue || [])
        setPaymentMethodData(data.paymentMethods || [])
      }
    } catch (error) {
      console.error("Error fetching revenue data:", error)
      // Mock data for development
      setRevenueData([
        { month: "Jan", revenue: 45000000, payments: 120, target: 50000000 },
        { month: "Feb", revenue: 52000000, payments: 135, target: 50000000 },
        { month: "Mar", revenue: 48000000, payments: 128, target: 50000000 },
        { month: "Apr", revenue: 61000000, payments: 156, target: 55000000 },
        { month: "May", revenue: 55000000, payments: 142, target: 55000000 },
        { month: "Jun", revenue: 67000000, payments: 178, target: 60000000 },
        { month: "Jul", revenue: 59000000, payments: 165, target: 60000000 },
        { month: "Aug", revenue: 72000000, payments: 189, target: 65000000 },
        { month: "Sep", revenue: 68000000, payments: 175, target: 65000000 },
        { month: "Oct", revenue: 74000000, payments: 195, target: 70000000 },
        { month: "Nov", revenue: 71000000, payments: 188, target: 70000000 },
        { month: "Dec", revenue: 78000000, payments: 205, target: 75000000 },
      ])
      setPaymentMethodData([
        { method: "CASH", amount: 280000000, percentage: 45 },
        { method: "UZCARD", amount: 186000000, percentage: 30 },
        { method: "PAYME", amount: 93000000, percentage: 15 },
        { method: "CLICK", amount: 62000000, percentage: 10 },
      ])
    } finally {
      setLoading(false)
    }
  }, [timeRange])

  useEffect(() => {
    fetchRevenueData()
  }, [fetchRevenueData])

  // Calculate totals and growth
  const totalRevenue = revenueData.reduce((sum, item) => sum + item.revenue, 0)
  const totalPayments = revenueData.reduce((sum, item) => sum + item.payments, 0)
  const averageRevenue = totalRevenue / revenueData.length || 0
  
  const currentMonth = revenueData[revenueData.length - 1]
  const previousMonth = revenueData[revenueData.length - 2]
  const growthRate = previousMonth 
    ? ((currentMonth?.revenue - previousMonth.revenue) / previousMonth.revenue * 100).toFixed(1)
    : "0"

  // Colors for pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

  // Custom tooltip formatter
  const formatTooltip = (value: number, name: string) => {
    if (name === "revenue" || name === "target") {
      return [`${(value / 1000000).toFixed(1)}M UZS`, name === "revenue" ? "Revenue" : "Target"]
    }
    return [value, name === "payments" ? "Payments" : name]
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">Revenue Analytics</h3>
          <p className="text-sm text-gray-500">Track revenue trends and payment methods</p>
        </div>
        <div className="flex gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="6months">Last 6 Months</option>
            <option value="12months">Last 12 Months</option>
            <option value="24months">Last 24 Months</option>
          </select>
          <Button
            variant={chartType === "line" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("line")}
          >
            Line
          </Button>
          <Button
            variant={chartType === "bar" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("bar")}
          >
            Bar
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="text-2xl font-bold">
                {(totalRevenue / 1000000).toFixed(1)}M UZS
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Payments</p>
              <p className="text-2xl font-bold">{totalPayments.toLocaleString()}</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Average Monthly</p>
              <p className="text-2xl font-bold">
                {(averageRevenue / 1000000).toFixed(1)}M UZS
              </p>
            </div>
            <Calendar className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Growth Rate</p>
              <p className="text-2xl font-bold text-green-600">+{growthRate}%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-600" />
          </div>
        </Card>
      </div>

      {/* Revenue Trend Chart */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Revenue Trend</h4>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={400}>
            {chartType === "line" ? (
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis 
                  tickFormatter={(value) => `${(value / 1000000).toFixed(0)}M`}
                />
                <Tooltip formatter={formatTooltip} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="#0088FE"
                  strokeWidth={3}
                  dot={{ fill: "#0088FE", strokeWidth: 2, r: 4 }}
                  name="Revenue"
                />
                <Line
                  type="monotone"
                  dataKey="target"
                  stroke="#FF8042"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "#FF8042", strokeWidth: 2, r: 3 }}
                  name="Target"
                />
              </LineChart>
            ) : (
              <BarChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis 
                  tickFormatter={(value) => `${(value / 1000000).toFixed(0)}M`}
                />
                <Tooltip formatter={formatTooltip} />
                <Legend />
                <Bar dataKey="revenue" fill="#0088FE" name="Revenue" />
                <Bar dataKey="target" fill="#FF8042" name="Target" />
              </BarChart>
            )}
          </ResponsiveContainer>
        )}
      </Card>

      {/* Payment Methods Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="text-lg font-medium mb-4">Payment Methods Distribution</h4>
          {loading ? (
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={paymentMethodData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ method, percentage }) => `${method} (${percentage}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="amount"
                >
                  {paymentMethodData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number) => [`${(value / 1000000).toFixed(1)}M UZS`, "Amount"]}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
        </Card>

        <Card className="p-6">
          <h4 className="text-lg font-medium mb-4">Payment Methods Breakdown</h4>
          <div className="space-y-4">
            {paymentMethodData.map((method, index) => (
              <div key={method.method} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <span className="font-medium">{method.method}</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">
                    {(method.amount / 1000000).toFixed(1)}M UZS
                  </div>
                  <div className="text-sm text-gray-500">{method.percentage}%</div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  )
}
