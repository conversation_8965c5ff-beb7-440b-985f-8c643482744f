const fs = require('fs');
const path = require('path');

// Function to fix malformed imports in a file
function fixMalformedImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Fix any excessive ../../../ patterns back to correct relative paths
    let fixedContent = content
      // Fix any path with more than 10 ../ back to reasonable paths
      .replace(/(\.\.\/)+(\.\.\/){5,}/g, '../../../../')
      // Fix specific malformed patterns
      .replace(/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/components/g, '../../../../components')
      .replace(/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/lib/g, '../../../../lib')
      .replace(/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/contexts/g, '../../../../contexts');
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed malformed imports in: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No malformed imports found: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to recursively find all TypeScript/React files
function findTsxFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (!['node_modules', '.next', '.git'].includes(file)) {
        findTsxFiles(filePath, fileList);
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

console.log('🔧 Finding and fixing all malformed imports...\n');

// Find all files in app directory
const appFiles = findTsxFiles('app');

console.log(`Found ${appFiles.length} TypeScript/React files in app directory\n`);

let fixedCount = 0;
appFiles.forEach(filePath => {
  if (fixMalformedImports(filePath)) {
    fixedCount++;
  }
});

console.log(`\n🎯 Summary: Fixed malformed imports in ${fixedCount} files out of ${appFiles.length} checked`);
