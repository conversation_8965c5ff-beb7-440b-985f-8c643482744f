import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '../../../../../lib/auth'
import { prisma } from '../../../../../lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Find the student profile for the current user
    const student = await prisma.student.findUnique({
      where: { userId: session.user.id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            phone: true
          }
        }
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student profile not found' }, { status: 404 })
    }

    // Mock data for now until database schema is complete
    const totalClasses = 20
    const attendedClasses = 18
    const attendanceRate = Math.round((attendedClasses / totalClasses) * 100)
    const upcomingClasses = 3
    const pendingAssignments = 2
    const averageScore = 75

    // Determine next level based on current level
    const levelProgression = {
      'A1': 'A2',
      'A2': 'B1',
      'B1': 'B2',
      'B2': 'IELTS',
      'IELTS': 'Advanced',
      'SAT': 'SAT Advanced',
      'MATH': 'Advanced Math',
      'KIDS': 'Kids Advanced'
    }

    const nextLevel = levelProgression[student.level as keyof typeof levelProgression] || 'Advanced'

    // Mock recent classes
    const recentClasses = [
      { date: '2024-01-15', topic: 'Grammar Practice', status: 'present' },
      { date: '2024-01-13', topic: 'Vocabulary Building', status: 'present' },
      { date: '2024-01-11', topic: 'Reading Comprehension', status: 'absent' },
      { date: '2024-01-09', topic: 'Speaking Practice', status: 'present' }
    ]

    // Mock payment data
    const totalPayments = 2400000
    const paidAmount = 1800000
    const pendingAmount = totalPayments - paidAmount

    const dashboardData = {
      student: {
        name: student.user.name,
        level: student.level,
        nextLevel,
        branch: student.branch
      },
      progress: {
        overall: averageScore,
        attendance: attendanceRate,
        averageScore: averageScore
      },
      stats: {
        totalClasses,
        attendedClasses,
        upcomingClasses,
        pendingAssignments
      },
      payments: {
        totalPayments,
        paidAmount,
        pendingAmount
      },
      recentClasses,
      currentEnrollment: {
        groupName: 'B1 Morning Group',
        courseName: 'General English B1',
        teacherName: 'Ms. Sarah Johnson'
      }
    }

    return NextResponse.json(dashboardData)
  } catch (error) {
    console.error('Error fetching student dashboard data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
