# 🔧 Authentication Fix Guide

## 🚨 Issues Fixed

### 1. **Environment Variable Mismatch**
- ✅ Fixed `INTER_SERVER_KEY` vs `INTER_SERVER_SECRET` inconsistency
- ✅ Updated auth verification endpoint to use correct variable

### 2. **Circular Dependency Problem**
- ✅ Added local database authentication as primary method
- ✅ Inter-server authentication now works as fallback for staff server

### 3. **Missing Local Authentication**
- ✅ Both servers can now authenticate users from their own databases
- ✅ Role-based access control implemented per server type

## 🔑 Required Environment Variables

### **Admin Server** (inno-crm-admin.vercel.app)
```env
# Core Authentication
DATABASE_URL="your-admin-database-url"
NEXTAUTH_SECRET="your-super-secret-key-32-characters-minimum"
NEXTAUTH_URL="https://inno-crm-admin.vercel.app"

# Server Configuration
SERVER_TYPE="admin"
PRISMA_GENERATE_DATAPROXY="true"

# Inter-Server Communication (Optional but recommended)
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="your-shared-secret-for-server-communication"
```

### **Staff Server** (inno-crm-staff.vercel.app)
```env
# Core Authentication
DATABASE_URL="your-staff-database-url"
NEXTAUTH_SECRET="your-super-secret-key-32-characters-minimum"
NEXTAUTH_URL="https://inno-crm-staff.vercel.app"

# Server Configuration
SERVER_TYPE="staff"
PRISMA_GENERATE_DATAPROXY="true"

# Inter-Server Communication (Required for fallback auth)
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="your-shared-secret-for-server-communication"
```

## 🔄 How Authentication Now Works

### **Admin Server Authentication**
1. **Primary**: Local database authentication
2. **Roles Allowed**: `ADMIN`, `CASHIER`, `MANAGER`, `TEACHER`, `RECEPTION`, `STUDENT`, `ACADEMIC_MANAGER`
3. **Fallback**: None needed (admin has all users)

### **Staff Server Authentication**
1. **Primary**: Local database authentication
2. **Fallback**: Inter-server authentication with admin server
3. **Roles Allowed**: `MANAGER`, `TEACHER`, `RECEPTION`, `STUDENT`, `ACADEMIC_MANAGER`
4. **Roles Blocked**: `ADMIN`, `CASHIER`

## 🚀 Deployment Steps

### 1. **Update Environment Variables in Vercel**

For **Admin Server**:
```bash
# Go to Vercel Dashboard > inno-crm-admin > Settings > Environment Variables
# Add/Update these variables:
DATABASE_URL=your-admin-database-url
NEXTAUTH_SECRET=your-32-char-secret
NEXTAUTH_URL=https://inno-crm-admin.vercel.app
SERVER_TYPE=admin
INTER_SERVER_SECRET=your-shared-secret
```

For **Staff Server**:
```bash
# Go to Vercel Dashboard > inno-crm-staff > Settings > Environment Variables
# Add/Update these variables:
DATABASE_URL=your-staff-database-url
NEXTAUTH_SECRET=your-32-char-secret
NEXTAUTH_URL=https://inno-crm-staff.vercel.app
SERVER_TYPE=staff
ADMIN_SERVER_URL=https://inno-crm-admin.vercel.app
INTER_SERVER_SECRET=your-shared-secret
```

### 2. **Redeploy Both Applications**
```bash
# Trigger redeployment in Vercel to apply environment changes
# Or push a commit to trigger automatic deployment
```

### 3. **Seed Database with Users**
```bash
# For each server, run the seed script
npm run db:seed
```

## 🧪 Testing Authentication

### 1. **Run Authentication Test Script**
```bash
node scripts/test-authentication.js
```

### 2. **Test Default Credentials**
- **Admin**: Phone: `+998901234567`, Password: `admin123`
- **Manager**: Phone: `+998901234568`, Password: `manager123`
- **Reception**: Phone: `+998901234569`, Password: `reception123`

### 3. **Verify Role Access**
- **Admin Server**: All roles should work
- **Staff Server**: Only staff roles (no ADMIN/CASHIER)

## 🔍 Troubleshooting

### **Issue**: "Invalid phone number or password"
**Solution**: 
1. Check if users exist in database
2. Run `npm run db:seed` to create default users
3. Verify environment variables are set correctly

### **Issue**: "Server key mismatch"
**Solution**:
1. Ensure `INTER_SERVER_SECRET` is identical on both servers
2. Check that the variable name is correct (not `INTER_SERVER_KEY`)

### **Issue**: "Access denied: Role not allowed"
**Solution**:
1. Verify `SERVER_TYPE` environment variable is set correctly
2. Check user role matches allowed roles for that server type

### **Issue**: Database connection errors
**Solution**:
1. Verify `DATABASE_URL` is correct and accessible
2. Check that `PRISMA_GENERATE_DATAPROXY="true"` is set for Vercel
3. Run `npx prisma db push` to ensure schema is up to date

## 📋 Verification Checklist

- [ ] Environment variables set in Vercel for both servers
- [ ] `NEXTAUTH_SECRET` is different for each server
- [ ] `INTER_SERVER_SECRET` is identical for both servers
- [ ] `SERVER_TYPE` is set correctly (`admin` vs `staff`)
- [ ] Database URLs point to correct databases
- [ ] Both applications redeployed after env var changes
- [ ] Database seeded with default users
- [ ] Authentication test script passes
- [ ] Login works with default credentials
- [ ] Role-based access control working

## 🎯 Next Steps

1. **Set environment variables** in Vercel dashboard
2. **Redeploy both applications**
3. **Run database seed** on both servers
4. **Test authentication** with default credentials
5. **Verify role-based access** is working correctly

The authentication system should now work independently on each server with proper fallback mechanisms!
