import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '../../../../lib/auth'
import { prisma } from '../../../../lib/prisma'
import { ActivityLogger } from '../../../../lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const updateLeadSchema = z.object({
  status: z.enum(['NEW', 'CALLING', 'CALL_COMPLETED', 'GROUP_ASSIGNED', 'ARCHIVED', 'NOT_INTERESTED']).optional(),
  notes: z.string().optional(),
  assignedTo: z.string().optional(),
  callStartedAt: z.string().optional(),
  callEndedAt: z.string().optional(),
  callDuration: z.number().optional(),
  assignedGroupId: z.string().optional(),
  assignedTeacherId: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const lead = await prisma.lead.findUnique({
      where: { id },
    })

    if (!lead) {
      return NextResponse.json(
        { error: 'Lead not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(lead)
  } catch (error) {
    console.error('Error fetching lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateLeadSchema.parse(body)

    // Get the existing lead for comparison
    const existingLead = await prisma.lead.findUnique({
      where: { id },
    })

    if (!existingLead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    const updateData: any = {
      ...validatedData,
      updatedAt: new Date(),
    }

    // Convert date strings to Date objects
    if (validatedData.callStartedAt) {
      updateData.callStartedAt = new Date(validatedData.callStartedAt)
    }
    if (validatedData.callEndedAt) {
      updateData.callEndedAt = new Date(validatedData.callEndedAt)
    }

    // Set archivedAt when status changes to ARCHIVED
    if (validatedData.status === 'ARCHIVED' && existingLead.status !== 'ARCHIVED') {
      updateData.archivedAt = new Date()
    }

    // Set assignedAt when group is assigned
    if (validatedData.assignedGroupId && !existingLead.assignedGroupId) {
      updateData.assignedAt = new Date()
    }

    const lead = await prisma.lead.update({
      where: { id },
      data: updateData,
      include: {
        assignedGroup: {
          include: {
            course: { select: { name: true, level: true } },
            teacher: { include: { user: { select: { name: true } } } }
          }
        },
        assignedTeacher: {
          include: { user: { select: { name: true } } }
        }
      }
    })

    // Log activity for different status changes
    if (validatedData.status && validatedData.status !== existingLead.status) {
      if (validatedData.status === 'CALLING') {
        await ActivityLogger.logLeadContacted(
          session.user.id,
          session.user.role as Role,
          lead.id,
          {
            leadName: lead.name,
            leadPhone: lead.phone,
            previousStatus: existingLead.status,
            newStatus: lead.status,
            notes: 'Call started',
          },
          request
        )
      } else if (validatedData.status === 'GROUP_ASSIGNED') {
        await ActivityLogger.logLeadContacted(
          session.user.id,
          session.user.role as Role,
          lead.id,
          {
            leadName: lead.name,
            leadPhone: lead.phone,
            previousStatus: existingLead.status,
            newStatus: lead.status,
            notes: `Assigned to group: ${lead.assignedGroup?.name || 'Unknown'}`,
          },
          request
        )
      }
    }

    return NextResponse.json(lead)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    await prisma.lead.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Lead deleted successfully' })
  } catch (error) {
    console.error('Error deleting lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
