// Inter-Server Authentication Validation Endpoint
// Allows staff server to validate users against admin server

import { NextRequest, NextResponse } from 'next/server';
import { validateInterServerAuth, InterServerUtils } from '../../../../../lib/inter-server';
import bcrypt from 'bcryptjs';
import { prisma } from '../../../../../lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Validate inter-server authentication
    if (!validateInterServerAuth(request)) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Unauthorized');
      return NextResponse.json(
        { error: 'Unauthorized inter-server request' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { phone, password } = body;

    if (!phone || !password) {
      return NextResponse.json(
        { error: 'Phone and password are required' },
        { status: 400 }
      );
    }

    // Find user in database
    const user = await prisma.user.findUnique({
      where: { phone },
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        role: true,
        password: true,
        studentProfile: {
          select: {
            branch: true,
          }
        },
        teacherProfile: {
          select: {
            branch: true,
          }
        }
      }
    });

    if (!user) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'User not found');
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Note: User model doesn't have isActive field - users are considered active by default
    // If needed, this could be implemented through role-based logic or profile status

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);

    if (!isValidPassword) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Invalid password');
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Check if user role is allowed on requesting server
    const requestingServer = request.headers.get('User-Agent')?.includes('staff') ? 'staff' : 'admin';
    const staffRoles = ['RECEPTION', 'ACADEMIC_MANAGER', 'TEACHER', 'MANAGER', 'STUDENT'];
    const adminRoles = ['ADMIN', 'CASHIER'];

    if (requestingServer === 'staff' && !staffRoles.includes(user.role)) {
      InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Role not allowed on staff server');
      return NextResponse.json(
        { error: 'Access denied for this server' },
        { status: 403 }
      );
    }

    // Return user data (excluding password) and include branch info from profile
    const { password: _, studentProfile, teacherProfile, ...userWithoutPassword } = user;

    // Get branch from student or teacher profile
    const branch = studentProfile?.branch || teacherProfile?.branch || null;

    InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', true, `User ${user.id} validated`);

    return NextResponse.json({
      success: true,
      user: {
        ...userWithoutPassword,
        branch: branch
      },
    });

  } catch (error) {
    console.error('Inter-server auth validation error:', error);
    InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, error);
    
    return NextResponse.json(
      { 
        error: 'Authentication validation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
